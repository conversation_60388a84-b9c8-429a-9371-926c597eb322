<?php
// templates/game.php

require_once __DIR__ . '/../api/db_connect.php';

$user_id = $_SESSION['user_id'];
$stmt_user = $pdo->prepare("SELECT * FROM users WHERE id = :user_id;");
$stmt_user->execute([':user_id' => $user_id]);
$user = $stmt_user->fetch(PDO::FETCH_ASSOC);

// Get clan data
$clan_id = $_SESSION['clan_id'];
$stmt_clan = $pdo->prepare("SELECT * FROM clans WHERE id = :clan_id;");
$stmt_clan->execute([':clan_id' => $clan_id]);
$clan = $stmt_clan->fetch(PDO::FETCH_ASSOC);

// Get dashboard data
$stmt_dashboard = $pdo->query("
    SELECT c.clan_name AS name, gs.total_marks
    FROM game_state gs
    JOIN clans c ON gs.clan_id = c.id
    ORDER BY gs.total_marks DESC;
");
$dashboard_data = $stmt_dashboard->fetchAll(PDO::FETCH_ASSOC);

?>

<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <title>功德迴戰</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="game-container">
        <!-- Left Column -->
        <div class="left-panel">
            <div class="user-info-panel">
                <h2>玩家資訊</h2>
                <p><strong>玩家：</strong> <span id="username-display"><?php echo htmlspecialchars($user['username']); ?></span></p>
                <p><strong>根據地：</strong> <span><?php echo htmlspecialchars($clan['clan_name']); ?></span></p>
                <p><strong>等級：</strong> <span id="level-display"><?php echo $user['level']; ?></span></p>
                <div class="xp-bar-container">
                    <div id="xp-bar" class="xp-bar"></div>
                    <span id="xp-text" class="xp-text"><?php echo $user['experience'] . ' / ' . $user['level_up_threshold']; ?></span>
                </div>
                <p><strong>舍利子：</strong> <span id="prestige-points-display"><?php echo $user['prestige_points']; ?></span> (每點+1%功德)</p>
                <a href="logout.php" class="logout-btn">登出</a>
            </div>
            <div class="leaderboard-panel">
                <h2>十八區功德榜</h2>
                <table id="leaderboard-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>根據地</th>
                            <th>總功德</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($dashboard_data as $index => $data): ?>
                            <tr>
                                <td><?php echo $index + 1; ?></td>
                                <td><?php echo htmlspecialchars($data['name']); ?></td>
                                <td><?php echo htmlspecialchars($data['total_marks']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Center Column -->
        <div class="center-panel">
            <div class="score-panel">
                <h1 id="merit-score-display"><?php echo number_format($user['money']); ?></h1>
                <p>功德</p>
                <p id="merit-per-second-display">每秒功德：0</p>
            </div>
            <div class="mokugyo-area">
                <div id="mokugyo" class="mokugyo">
                    <img src="img/mokugyo.png" alt="Mokugyo">
                </div>
                <div id="plus-one" class="plus-one">功德+1</div>
                <div id="golden-mokugyo" class="golden-mokugyo" style="display: none;">
                    <img src="img/golden-mokugyo.png" alt="Golden Mokugyo">
                </div>
                <!-- Debug buttons -->
                <div style="margin-top: 10px; text-align: center;">
                    <button onclick="debugMeritSync()" style="background: #ff4444; color: white; padding: 5px 10px; border: none; border-radius: 3px; font-size: 12px;">Debug Merit</button>
                    <button onclick="testMokugyoClick()" style="background: #44ff44; color: white; padding: 5px 10px; border: none; border-radius: 3px; font-size: 12px;">Test Click</button>
                    <button onclick="testDBTransaction()" style="background: #ff8800; color: white; padding: 5px 10px; border: none; border-radius: 3px; font-size: 12px;">Test DB</button>
                    <button onclick="window.open('test_merit_sync.php', '_blank')" style="background: #4444ff; color: white; padding: 5px 10px; border: none; border-radius: 3px; font-size: 12px;">Merit Test</button>
                </div>
            </div>
            <div class="tabs-panel">
                <div class="tabs">
                    <button class="tab-link active" data-tab="stats-tab">統計</button>
                    <button class="tab-link" data-tab="missions-tab">任務</button>
                    <button class="tab-link" data-tab="achievements-tab">成就</button>
                    <button class="tab-link" data-tab="prestige-tab">輪迴</button>
                </div>
                <div id="stats-tab" class="tab-content active">
                    <!-- Stats content will be loaded by JS -->
                </div>
                <div id="missions-tab" class="tab-content">
                    <!-- Missions content will be loaded by JS -->
                </div>
                <div id="achievements-tab" class="tab-content">
                    <!-- Achievements content will be loaded by JS -->
                </div>
                <div id="prestige-tab" class="tab-content">
                    <p>當你達到 50 級時，你可以選擇輪迴。</p>
                    <p>輪迴會重置你的等級、功德、香油錢和技能，但你會獲得舍利子，永久增加你未來的功德收益。</p>
                    <button id="prestige-button" disabled>輪迴 (需要 50 級)</button>
                </div>
            </div>
        </div>

        <!-- Right Column -->
        <div class="right-panel">
            <div class="tabs">
                <button class="tab-link active" data-tab="buildings-tab">建築</button>
                <button class="tab-link" data-tab="upgrades-tab">升級</button>
                <button class="tab-link" data-tab="shop-tab">商店</button>
            </div>
            <div id="buildings-tab" class="tab-content active">
                <!-- Buildings content will be loaded by JS -->
            </div>
            <div id="upgrades-tab" class="tab-content">
                <!-- Upgrades (skills) content will be loaded by JS -->
            </div>
            <div id="shop-tab" class="tab-content">
                <!-- Shop items content will be loaded by JS -->
            </div>
        </div>
    </div>

    <audio id="click-sound" src="sound/click.mp3" preload="auto"></audio>
    <audio id="level-up-sound" src="sound/level_up.mp3" preload="auto"></audio>

    <script src="js/script.js"></script>
</body>
</html>