<?php
// templates/clan_selection.php

session_start();

require_once $_SERVER['DOCUMENT_ROOT'] . '/api/db_connect.php';

$stmt = $pdo->query("SELECT id, clan_name AS name FROM clans ORDER BY id;");
$clans = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['clan_id'])) {
    $clan_id = $_POST['clan_id'];
    $user_id = $_SESSION['user_id'];

    $stmt_update_user = $pdo->prepare("UPDATE users SET clan_id = :clan_id WHERE id = :user_id;");
    $stmt_update_user->execute([':clan_id' => $clan_id, ':user_id' => $user_id]);

    $_SESSION['clan_id'] = $clan_id;
    header('Location: /index.php');
    exit;
}

?>

<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>選擇你的根據地</title>
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <div class="container">
        <h1>選擇你的根據地</h1>
        <form action="clan_selection.php" method="post">
            <div class="clans-container">
                <?php foreach ($clans as $clan): ?>
                    <div class="clan-card">
                        <input type="radio" name="clan_id" value="<?php echo $clan['id']; ?>" id="clan_<?php echo $clan['id']; ?>">
                        <label for="clan_<?php echo $clan['id']; ?>"><?php echo $clan['name']; ?></label>
                    </div>
                <?php endforeach; ?>
            </div>
            <button type="submit">確定</button>
        </form>
    </div>
</body>
</html>