<?php
// templates/clan_selection.php

session_start();

require_once __DIR__ . '/../api/db_connect.php';

$stmt = $pdo->query("SELECT id, clan_name AS name FROM clans ORDER BY id;");
$clans = $stmt->fetchAll(PDO::FETCH_ASSOC);

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['clan_id'])) {
    $clan_id = $_POST['clan_id'];
    $user_id = $_SESSION['user_id'];

    $stmt_update_user = $pdo->prepare("UPDATE users SET clan_id = :clan_id WHERE id = :user_id;");
    $stmt_update_user->execute([':clan_id' => $clan_id, ':user_id' => $user_id]);

    $_SESSION['clan_id'] = $clan_id;
    header('Location: ../index.php');
    exit;
}

?>

<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>選擇你的根據地</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="container">
        <h1>選擇你的根據地</h1>
        <p style="text-align: center; color: var(--text-dark); margin-bottom: 25px;">
            選擇一個香港地區作為你的修行根據地，與同區的修行者一起競爭功德榜！
        </p>
        <form action="clan_selection.php" method="post" id="clan-form">
            <div class="clan-grid">
                <?php foreach ($clans as $clan): ?>
                    <div class="clan-option" data-clan-id="<?php echo $clan['id']; ?>">
                        <input type="radio" name="clan_id" value="<?php echo $clan['id']; ?>" id="clan_<?php echo $clan['id']; ?>" style="display: none;">
                        <label for="clan_<?php echo $clan['id']; ?>" style="cursor: pointer; display: block;">
                            <?php echo htmlspecialchars($clan['name']); ?>
                        </label>
                    </div>
                <?php endforeach; ?>
            </div>
            <button type="submit" class="submit-btn" id="submit-btn" disabled>
                確定選擇
            </button>
        </form>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const clanOptions = document.querySelectorAll('.clan-option');
            const submitBtn = document.getElementById('submit-btn');

            clanOptions.forEach(option => {
                option.addEventListener('click', function() {
                    // Remove selected class from all options
                    clanOptions.forEach(opt => opt.classList.remove('selected'));

                    // Add selected class to clicked option
                    this.classList.add('selected');

                    // Check the radio button
                    const radio = this.querySelector('input[type="radio"]');
                    radio.checked = true;

                    // Enable submit button
                    submitBtn.disabled = false;
                    submitBtn.textContent = '確定選擇 - ' + this.textContent.trim();
                });
            });
        });
    </script>
    </div>
</body>
</html>