/* --- General --- */
:root {
    --bg-dark: #121212;
    --bg-panel: #1E1E1E;
    --bg-panel-light: #2a2a2a;
    --border-color: #444;
    --text-light: #E0E0E0;
    --text-dark: #9E9E9E;
    --primary-color: #FFC107; /* Amber */
    --secondary-color: #03A9F4; /* Light Blue */
    --red: #f44336;
    --green: #4CAF50;
}

/* --- Mobile-first optimizations --- */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
}

html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    font-family: 'Noto Sans TC', sans-serif;
    background-color: var(--bg-dark);
    color: var(--text-light);
    margin: 0;
    overflow: hidden; /* Prevent scrollbars from main layout */
}

.game-container {
    display: grid;
    grid-template-columns: 300px 1fr 350px;
    height: 100vh;
    gap: 10px;
    padding: 10px;
}

/* --- Panels --- */
.left-panel, .center-panel, .right-panel {
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow: hidden;
}

.user-info-panel, .leaderboard-panel, .score-panel, .mokugyo-area, .tabs-panel, .right-panel .tabs, .right-panel .tab-content {
    background-color: var(--bg-panel);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 15px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

/* --- Left Panel --- */
.user-info-panel h2, .leaderboard-panel h2 {
    color: var(--primary-color);
    margin-top: 0;
    font-size: 1.2em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
}

.user-info-panel p {
    margin: 8px 0;
    color: var(--text-dark);
}
.user-info-panel p strong {
    color: var(--text-light);
}

.logout-btn {
    display: block;
    text-align: center;
    margin-top: 15px;
    padding: 8px;
    background-color: var(--secondary-color);
    color: var(--bg-dark);
    text-decoration: none;
    border-radius: 5px;
    font-weight: bold;
    transition: background-color 0.2s;
}
.logout-btn:hover {
    background-color: #29b6f6;
}

.leaderboard-panel {
    flex-grow: 1;
    overflow-y: auto;
}

#leaderboard-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 0.9em;
}
#leaderboard-table th, #leaderboard-table td {
    padding: 8px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}
#leaderboard-table th {
    color: var(--secondary-color);
}

/* --- Center Panel --- */
.score-panel {
    text-align: center;
}
.score-panel h1 {
    margin: 0;
    font-size: 3.5em;
    color: var(--primary-color);
}
.score-panel p {
    margin: 5px 0;
    color: var(--text-dark);
}

.mokugyo-area {
    flex-grow: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
}

.mokugyo {
    cursor: pointer;
    transition: transform 0.1s ease-out;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
    touch-action: manipulation;
}

.mokugyo.loading {
    opacity: 0.7;
    transform: scale(0.98);
}

.mokugyo.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #FFC107;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
.mokugyo:active {
    transform: scale(0.95);
}
.mokugyo img {
    width: 250px;
    height: auto;
    filter: drop-shadow(0 0 15px rgba(255, 193, 7, 0.3));
    pointer-events: none; /* Prevent image drag on mobile */
}

.plus-one {
    position: absolute;
    font-size: 2.5em;
    color: var(--primary-color);
    opacity: 0;
    pointer-events: none;
    white-space: nowrap;
    transform: translateY(0);
    font-weight: bold;
    text-shadow: 0 0 5px rgba(0,0,0,0.5);
}
.plus-one.animate {
    animation: fade-up-and-out 1s forwards;
}

@keyframes fade-up-and-out {
    0% { opacity: 1; transform: translateY(0); }
    100% { opacity: 0; transform: translateY(-80px); }
}

.golden-mokugyo {
    position: absolute;
    cursor: pointer;
    z-index: 100;
    animation: shimmer 1.5s infinite, move-around 10s infinite linear;
}
.golden-mokugyo img {
    width: 80px;
    height: auto;
    filter: drop-shadow(0 0 20px gold);
}

@keyframes shimmer {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.5); }
}
@keyframes move-around {
    0% { top: 10%; left: 10%; }
    25% { top: 70%; left: 80%; }
    50% { top: 50%; left: 20%; }
    75% { top: 20%; left: 70%; }
    100% { top: 10%; left: 10%; }
}

/* --- Right Panel & Tabs --- */
.tabs {
    display: flex;
    background-color: var(--bg-panel);
    border-radius: 8px 8px 0 0;
    padding: 0;
    border: 1px solid var(--border-color);
    border-bottom: none;
}

.tab-link {
    flex-grow: 1;
    padding: 12px;
    cursor: pointer;
    background-color: transparent;
    border: none;
    color: var(--text-dark);
    font-size: 1em;
    transition: background-color 0.2s, color 0.2s;
    border-bottom: 3px solid transparent;
}
.tab-link.active {
    color: var(--primary-color);
    border-bottom: 3px solid var(--primary-color);
}
.tab-link:hover {
    background-color: var(--bg-panel-light);
}

.tab-content {
    display: none;
    padding: 15px;
    background-color: var(--bg-panel);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 8px 8px;
    flex-grow: 1;
    overflow-y: auto;
}
.tab-content.active {
    display: block;
}

/* --- Item/Building/Upgrade Cards --- */
.item-card {
    background-color: var(--bg-panel-light);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.item-info h3 {
    margin: 0 0 5px 0;
    color: var(--text-light);
    font-size: 1.1em;
}
.item-info p {
    margin: 0;
    font-size: 0.9em;
    color: var(--text-dark);
}
.item-info .item-level {
    font-size: 1.5em;
    font-weight: bold;
    color: var(--secondary-color);
}

.buy-button {
    padding: 10px 15px;
    background-color: var(--primary-color);
    color: var(--bg-dark);
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: background-color 0.2s;
}
.buy-button:hover:not(:disabled) {
    background-color: #ffca28;
}
.buy-button:disabled {
    background-color: #555;
    color: #888;
    cursor: not-allowed;
}

/* --- XP Bar --- */
.xp-bar-container {
    width: 100%;
    background-color: #333;
    border-radius: 5px;
    margin-top: 5px;
    height: 18px;
    position: relative;
    overflow: hidden;
}
.xp-bar {
    width: 0%;
    height: 100%;
    background-color: var(--secondary-color);
    border-radius: 5px;
    transition: width 0.5s ease-in-out;
}
.xp-text {
    position: absolute;
    width: 100%;
    text-align: center;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0.8em;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.7);
    font-weight: bold;
}

/* --- Passive Income Notification Animations --- */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

.passive-income-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    font-weight: bold;
    z-index: 1000;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    animation: slideInRight 0.5s ease-out;
}

/* Level Up Notification */
@keyframes levelUpPulse {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
}

@keyframes fadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

.level-up-notification {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #333;
    padding: 20px 30px;
    border-radius: 12px;
    font-weight: bold;
    font-size: 1.5em;
    z-index: 1001;
    box-shadow: 0 8px 20px rgba(0,0,0,0.4);
    animation: levelUpPulse 2s ease-out;
}

/* --- Responsive Design --- */
@media (max-width: 1200px) {
    .game-container {
        grid-template-columns: 250px 1fr 300px;
    }
}

@media (max-width: 992px) {
    .game-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto auto;
        height: auto;
        overflow-y: auto;
        padding: 5px;
        gap: 5px;
    }
    .left-panel, .center-panel, .right-panel {
        overflow: visible;
    }

    /* Make panels more compact on tablets */
    .user-info-panel, .leaderboard-panel, .score-panel, .mokugyo-area, .tabs-panel {
        padding: 10px;
    }
}

/* --- Mobile Optimizations --- */
@media (max-width: 768px) {
    body {
        overflow: auto; /* Allow scrolling on mobile */
    }

    .game-container {
        display: flex;
        flex-direction: column;
        height: auto;
        min-height: 100vh;
        padding: 5px;
        gap: 10px;
    }

    /* Mobile-first layout order */
    .center-panel {
        order: 1; /* Mokugyo area first */
    }
    .right-panel {
        order: 2; /* Buildings/upgrades second */
    }
    .left-panel {
        order: 3; /* User info last */
    }

    /* Score panel - make it more prominent on mobile */
    .score-panel {
        padding: 15px;
        text-align: center;
        background: linear-gradient(135deg, var(--bg-panel), var(--bg-panel-light));
    }
    .score-panel h1 {
        font-size: 2.2em;
        margin: 5px 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }
    .score-panel p {
        font-size: 1.1em;
        margin: 3px 0;
    }

    /* Mokugyo area - optimize for mobile tapping */
    .mokugyo-area {
        min-height: 300px;
        padding: 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: radial-gradient(circle, var(--bg-panel-light), var(--bg-panel));
    }
    .mokugyo {
        transform: scale(1);
        transition: transform 0.15s ease-out;
    }
    .mokugyo:active {
        transform: scale(0.9);
    }
    .mokugyo img {
        width: 180px;
        height: auto;
        filter: drop-shadow(0 0 20px rgba(255, 193, 7, 0.4));
    }

    /* User info panel - compact mobile version */
    .user-info-panel {
        padding: 12px;
    }
    .user-info-panel h2 {
        font-size: 1.1em;
        margin-bottom: 8px;
    }
    .user-info-panel p {
        margin: 4px 0;
        font-size: 0.9em;
    }

    /* XP Bar - make it more visible on mobile */
    .xp-bar-container {
        height: 20px;
        margin: 8px 0;
    }
    .xp-text {
        font-size: 0.85em;
    }

    /* Leaderboard - make it scrollable and compact */
    .leaderboard-panel {
        max-height: 250px;
        overflow-y: auto;
    }
    #leaderboard-table {
        font-size: 0.85em;
    }
    #leaderboard-table th, #leaderboard-table td {
        padding: 6px 4px;
    }

    /* Tabs - make them touch-friendly */
    .tabs {
        display: flex;
        flex-wrap: wrap;
    }
    .tab-link {
        flex: 1;
        min-width: 80px;
        padding: 12px 8px;
        font-size: 0.9em;
        text-align: center;
    }

    /* Tab content - optimize for mobile scrolling */
    .tab-content {
        max-height: 400px;
        overflow-y: auto;
        padding: 10px;
    }

    /* Item cards - make them touch-friendly */
    .item-card {
        padding: 12px;
        margin-bottom: 8px;
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    .item-info {
        text-align: center;
    }
    .item-info h3 {
        font-size: 1em;
        margin-bottom: 8px;
    }
    .item-info p {
        font-size: 0.85em;
        margin: 2px 0;
    }

    /* Buttons - make them larger for touch */
    .buy-button, .claim-button {
        padding: 12px 20px;
        font-size: 1em;
        min-height: 44px; /* iOS recommended touch target size */
        border-radius: 8px;
    }

    /* Plus one animation - adjust for mobile */
    .plus-one {
        font-size: 2em;
        font-weight: bold;
        text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    }

    /* Mobile-specific improvements */
    .game-container {
        -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
    }

    /* Prevent zoom on input focus (mobile) */
    input, select, textarea {
        font-size: 16px !important;
    }

    /* Mobile-friendly button spacing */
    .buy-button, .claim-button, .tab-link {
        -webkit-tap-highlight-color: rgba(255, 193, 7, 0.2);
        touch-action: manipulation;
    }

    /* Improve readability on small screens */
    .score-panel p#merit-per-second-display {
        font-size: 1em;
        font-weight: 500;
        color: var(--secondary-color);
    }
}

/* --- Extra small mobile devices --- */
@media (max-width: 480px) {
    .game-container {
        padding: 3px;
        gap: 8px;
    }

    .score-panel h1 {
        font-size: 1.8em;
    }

    .mokugyo img {
        width: 150px;
    }

    .mokugyo-area {
        min-height: 250px;
        padding: 15px;
    }

    .tab-content {
        max-height: 300px;
    }

    .item-card {
        padding: 10px;
    }

    .plus-one {
        font-size: 1.5em;
    }

    /* Make sure text is readable */
    .user-info-panel p, .item-info p {
        font-size: 0.8em;
    }

    /* Compact leaderboard for very small screens */
    .leaderboard-panel {
        max-height: 200px;
    }
    #leaderboard-table {
        font-size: 0.75em;
    }
}

/* --- NEW FEATURES STYLES --- */

/* Inventory System */
.inventory-filters {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.filter-btn {
    padding: 6px 12px;
    background: var(--bg-panel-light);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    color: var(--text-light);
    cursor: pointer;
    font-size: 0.9em;
    transition: all 0.2s ease;
}

.filter-btn:hover {
    background: var(--primary-color);
    color: var(--bg-dark);
}

.filter-btn.active {
    background: var(--primary-color);
    color: var(--bg-dark);
    border-color: var(--primary-color);
}

.inventory-stats {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    font-size: 0.9em;
    color: var(--text-dark);
}

.inventory-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 8px;
    max-height: 400px;
    overflow-y: auto;
}

.inventory-item {
    position: relative;
    background: var(--bg-panel-light);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    padding: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.inventory-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.inventory-item.rarity-common { border-color: #9E9E9E; }
.inventory-item.rarity-uncommon { border-color: #4CAF50; }
.inventory-item.rarity-rare { border-color: #2196F3; }
.inventory-item.rarity-epic { border-color: #9C27B0; }
.inventory-item.rarity-legendary { border-color: #FF9800; }

.inventory-item .item-icon {
    width: 32px;
    height: 32px;
    background: var(--primary-color);
    border-radius: 4px;
    margin-bottom: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

.inventory-item .item-name {
    font-size: 0.7em;
    color: var(--text-light);
    margin-bottom: 2px;
    line-height: 1.2;
}

.inventory-item .item-quantity {
    position: absolute;
    bottom: 2px;
    right: 4px;
    background: var(--bg-dark);
    color: var(--primary-color);
    font-size: 0.7em;
    padding: 1px 4px;
    border-radius: 3px;
    font-weight: bold;
}

/* Item tooltip */
.item-tooltip {
    position: absolute;
    background: var(--bg-dark);
    border: 1px solid var(--border-color);
    border-radius: 6px;
    padding: 10px;
    z-index: 1000;
    max-width: 250px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.5);
    pointer-events: none;
}

.item-tooltip .tooltip-name {
    color: var(--primary-color);
    font-weight: bold;
    margin-bottom: 5px;
}

.item-tooltip .tooltip-description {
    color: var(--text-light);
    font-size: 0.9em;
    margin-bottom: 8px;
    line-height: 1.3;
}

.item-tooltip .tooltip-stats {
    font-size: 0.8em;
    color: var(--text-dark);
}

.item-tooltip .tooltip-stats div {
    margin-bottom: 2px;
}

/* Login/Register/Clan Selection Pages */
.container {
    max-width: 400px;
    margin: 50px auto;
    padding: 30px;
    background-color: var(--bg-panel);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.3);
}

.container h1 {
    text-align: center;
    color: var(--primary-color);
    margin-bottom: 30px;
    font-size: 2.2em;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: var(--text-light);
    font-weight: 500;
    font-size: 1.1em;
}

.form-group input, .form-group select {
    width: 100%;
    padding: 12px 15px;
    background-color: var(--bg-panel-light);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    color: var(--text-light);
    font-size: 1em;
    transition: border-color 0.3s, box-shadow 0.3s;
    box-sizing: border-box;
}

.form-group input:focus, .form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(255, 193, 7, 0.1);
}

.form-group input::placeholder {
    color: var(--text-dark);
}

button[type="submit"], .submit-btn {
    width: 100%;
    padding: 15px;
    background: linear-gradient(135deg, var(--primary-color), #ffca28);
    color: var(--bg-dark);
    border: none;
    border-radius: 8px;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s, box-shadow 0.2s;
    margin-top: 10px;
}

button[type="submit"]:hover, .submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
}

button[type="submit"]:active, .submit-btn:active {
    transform: translateY(0);
}

.container p {
    text-align: center;
    margin-top: 20px;
    color: var(--text-dark);
}

.container p a {
    color: var(--secondary-color);
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s;
}

.container p a:hover {
    color: #29b6f6;
    text-decoration: underline;
}

.error {
    background-color: rgba(244, 67, 54, 0.1);
    border: 1px solid var(--red);
    color: var(--red);
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 500;
}

.success {
    background-color: rgba(76, 175, 80, 0.1);
    border: 1px solid var(--green);
    color: var(--green);
    padding: 12px;
    border-radius: 6px;
    margin-bottom: 20px;
    text-align: center;
    font-weight: 500;
}

/* Clan Selection Specific Styles */
.clan-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.clan-option {
    padding: 15px;
    background-color: var(--bg-panel-light);
    border: 2px solid var(--border-color);
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    color: var(--text-light);
    font-weight: 500;
}

.clan-option:hover {
    border-color: var(--primary-color);
    background-color: rgba(255, 193, 7, 0.1);
    transform: translateY(-2px);
}

.clan-option.selected {
    border-color: var(--primary-color);
    background-color: rgba(255, 193, 7, 0.2);
    color: var(--primary-color);
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid var(--border-color);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
