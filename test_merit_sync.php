<?php
// test_merit_sync.php - Test merit synchronization

session_start();
require_once './api/db_connect.php';

echo "<!DOCTYPE html>
<html lang='zh-Hant'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Merit Sync Test</title>
    <style>
        body { font-family: Arial, sans-serif; background: #121212; color: #E0E0E0; padding: 20px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #FFC107; }
        .info { color: #03A9F4; }
        h2 { color: #FFC107; border-bottom: 1px solid #444; padding-bottom: 10px; }
        pre { background: #1E1E1E; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .test-button { background: #FFC107; color: #121212; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-section { margin: 20px 0; padding: 15px; background: #1E1E1E; border-radius: 8px; }
    </style>
</head>
<body>";

echo "<h1>🔍 Merit Synchronization Test</h1>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p class='error'>❌ Please <a href='login.php' style='color: #03A9F4;'>login</a> first</p>";
    echo "</body></html>";
    exit;
}

$user_id = $_SESSION['user_id'];

// Test 1: Current User Data
echo "<div class='test-section'>";
echo "<h2>📊 Current User Data</h2>";
try {
    $stmt = $pdo->prepare("SELECT id, username, money, merit, level, experience, total_clicks, total_merit_earned FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "<p class='success'>✅ User data retrieved</p>";
        echo "<pre>";
        foreach ($user as $key => $value) {
            echo "$key: $value\n";
        }
        echo "</pre>";
        
        if ($user['money'] != $user['merit']) {
            echo "<p class='warning'>⚠️ Money and merit values are different!</p>";
            echo "<p class='info'>Money: {$user['money']}, Merit: {$user['merit']}</p>";
        } else {
            echo "<p class='success'>✅ Money and merit values are synchronized</p>";
        }
    } else {
        echo "<p class='error'>❌ User not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 2: API Test
echo "<div class='test-section'>";
echo "<h2>🔌 API Response Test</h2>";
echo "<button class='test-button' onclick='testMokugyoAPI()'>Test Mokugyo Click API</button>";
echo "<button class='test-button' onclick='testUserAPI()'>Test User Data API</button>";
echo "<div id='api-results'></div>";
echo "</div>";

// Test 3: Manual Merit Update
echo "<div class='test-section'>";
echo "<h2>✏️ Manual Merit Update Test</h2>";
echo "<button class='test-button' onclick='addMerit(10)'>Add 10 Merit</button>";
echo "<button class='test-button' onclick='addMerit(100)'>Add 100 Merit</button>";
echo "<button class='test-button' onclick='syncMeritFields()'>Sync Merit Fields</button>";
echo "<div id='manual-results'></div>";
echo "</div>";

echo "<script>
function testMokugyoAPI() {
    const resultsDiv = document.getElementById('api-results');
    resultsDiv.innerHTML = '<p class=\"info\">Testing mokugyo click API...</p>';
    
    fetch('api/update_score.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ increment: 1 })
    })
    .then(response => {
        console.log('API Response Status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('API Response Data:', data);
        resultsDiv.innerHTML = '<p class=\"success\">✅ API Response:</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        
        if (data.success && data.money) {
            resultsDiv.innerHTML += '<p class=\"info\">💰 New money value: ' + data.money + '</p>';
        }
        
        setTimeout(() => location.reload(), 2000);
    })
    .catch(error => {
        console.error('API Error:', error);
        resultsDiv.innerHTML = '<p class=\"error\">❌ API Error: ' + error.message + '</p>';
    });
}

function testUserAPI() {
    const resultsDiv = document.getElementById('api-results');
    resultsDiv.innerHTML = '<p class=\"info\">Testing user data API...</p>';
    
    fetch('api/user.php')
    .then(response => response.json())
    .then(data => {
        console.log('User API Data:', data);
        resultsDiv.innerHTML = '<p class=\"success\">✅ User API Response:</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
    })
    .catch(error => {
        console.error('User API Error:', error);
        resultsDiv.innerHTML = '<p class=\"error\">❌ User API Error: ' + error.message + '</p>';
    });
}

function addMerit(amount) {
    const resultsDiv = document.getElementById('manual-results');
    resultsDiv.innerHTML = '<p class=\"info\">Adding ' + amount + ' merit...</p>';
    
    fetch('test_merit_sync.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'add_merit=' + amount
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('SUCCESS')) {
            resultsDiv.innerHTML = '<p class=\"success\">✅ Merit added successfully</p>';
            setTimeout(() => location.reload(), 1000);
        } else {
            resultsDiv.innerHTML = '<p class=\"error\">❌ Failed to add merit: ' + data + '</p>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = '<p class=\"error\">❌ Error: ' + error.message + '</p>';
    });
}

function syncMeritFields() {
    const resultsDiv = document.getElementById('manual-results');
    resultsDiv.innerHTML = '<p class=\"info\">Syncing merit fields...</p>';
    
    fetch('test_merit_sync.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'sync_merit=1'
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('SUCCESS')) {
            resultsDiv.innerHTML = '<p class=\"success\">✅ Merit fields synchronized</p>';
            setTimeout(() => location.reload(), 1000);
        } else {
            resultsDiv.innerHTML = '<p class=\"error\">❌ Failed to sync: ' + data + '</p>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = '<p class=\"error\">❌ Error: ' + error.message + '</p>';
    });
}
</script>";

// Handle POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_merit'])) {
        $amount = (int)$_POST['add_merit'];
        try {
            $stmt = $pdo->prepare("UPDATE users SET money = money + ?, merit = money + ? WHERE id = ?");
            $result = $stmt->execute([$amount, $amount, $user_id]);
            if ($result) {
                echo "SUCCESS";
            } else {
                echo "FAILED";
            }
        } catch (Exception $e) {
            echo "ERROR: " . $e->getMessage();
        }
        exit;
    }
    
    if (isset($_POST['sync_merit'])) {
        try {
            $stmt = $pdo->prepare("UPDATE users SET merit = money WHERE id = ?");
            $result = $stmt->execute([$user_id]);
            if ($result) {
                echo "SUCCESS";
            } else {
                echo "FAILED";
            }
        } catch (Exception $e) {
            echo "ERROR: " . $e->getMessage();
        }
        exit;
    }
}

echo "<p class='info'><a href='index.php' style='color: #03A9F4;'>← Back to Game</a></p>";
echo "</body></html>";
?>
