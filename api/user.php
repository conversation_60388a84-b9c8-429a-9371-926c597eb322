<?php
header('Content-Type: application/json');
require_once 'db_connect.php';

$response = ['success' => false, 'message' => 'An unknown error occurred.'];

// Ensure $pdo is initialized and not null
if ($pdo === null) {
    $response['message'] = 'Database connection not established.';
    echo json_encode($response);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (isset($input['action'])) {
    switch ($input['action']) {
        case 'setClan':
            if (isset($input['username']) && isset($input['clan'])) {
                $username = $input['username'];
                $clanName = $input['clan'];

                try {
                    // Get clan_id from clan_name
                    $stmt = $pdo->prepare("SELECT id FROM clans WHERE clan_name = ?");
                    $stmt->execute([$clanName]);
                    $clan = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($clan) {
                        $clanId = $clan['id'];
                        // Update user's clan_id
                        $updateStmt = $pdo->prepare("UPDATE users SET clan_id = ? WHERE username = ?");
                        if ($updateStmt->execute([$clanId, $username])) {
                            $response['success'] = true;
                            $response['message'] = 'Clan updated successfully.';
                        } else {
                            $response['message'] = 'Failed to update user clan.';
                        }
                    } else {
                        $response['message'] = 'Clan not found.';
                    }
                } catch (PDOException $e) {
                    error_log("Set clan error: " . $e->getMessage());
                    $response['message'] = 'An error occurred while setting clan.';
                }
            } else {
                $response['message'] = 'Username and clan are required.';
            }
            break;

        case 'getClan':
            if (isset($input['username'])) {
                $username = $input['username'];
                try {
                    $stmt = $pdo->prepare("SELECT c.clan_name FROM users u JOIN clans c ON u.clan_id = c.id WHERE u.username = ?");
                    $stmt->execute([$username]);
                    $clan = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($clan) {
                        $response['success'] = true;
                        $response['clan'] = $clan['clan_name'];
                    } else {
                        $response['success'] = true; // Still success, but no clan set yet
                        $response['clan'] = null;
                        $response['message'] = 'User has no clan set.';
                    }
                } catch (PDOException $e) {
                    error_log("Get clan error: " . $e->getMessage());
                    $response['message'] = 'An error occurred while getting clan.';
                }
            } else {
                $response['message'] = 'Username is required to get clan.';
            }
            break;

        case 'getMerit':
            if (isset($input['username'])) {
                $username = $input['username'];
                try {
                    $stmt = $pdo->prepare("SELECT merit FROM users WHERE username = ?");
                    $stmt->execute([$username]);
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($user) {
                        $response['success'] = true;
                        $response['merit'] = $user['merit'];
                    } else {
                        $response['success'] = false;
                        $response['message'] = 'User not found.';
                    }
                } catch (PDOException $e) {
                    error_log("Get merit error: " . $e->getMessage());
                    $response['message'] = 'An error occurred while getting merit.';
                }
            } else {
                $response['message'] = 'Username is required to get merit.';
            }
            break;

        case 'getLevel':
            if (isset($input['username'])) {
                $username = $input['username'];
                try {
                    $stmt = $pdo->prepare("SELECT level FROM users WHERE username = ?");
                    $stmt->execute([$username]);
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($user) {
                        $response['success'] = true;
                        $response['level'] = $user['level'];
                    } else {
                        $response['success'] = false;
                        $response['message'] = 'User not found.';
                    }
                } catch (PDOException $e) {
                    error_log("Get level error: " . $e->getMessage());
                    $response['message'] = 'An error occurred while getting level.';
                }
            } else {
                $response['message'] = 'Username is required to get level.';
            }
            break;

        default:
            $response['message'] = 'Invalid action.';
            break;
    }
} else {
    $response['message'] = 'No action specified.';
}

echo json_encode($response);
?>