<?php
// public/api/update_score.php

session_start();

require_once 'db_connect.php';

function updateMissionProgress($pdo, $user_id, $mission_type, $progress_amount) {
    try {
        // Get user missions of this type that are not completed
        $stmt = $pdo->prepare("
            SELECT um.id, m.target, um.progress
            FROM user_missions um
            JOIN missions m ON um.mission_id = m.id
            WHERE um.user_id = :user_id
            AND m.type = :mission_type
            AND um.completed = FALSE
            AND um.assigned_at = CURRENT_DATE
        ");
        $stmt->execute([':user_id' => $user_id, ':mission_type' => $mission_type]);
        $missions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($missions as $mission) {
            $new_progress = $mission['progress'] + $progress_amount;
            $completed = $new_progress >= $mission['target'];

            $update_stmt = $pdo->prepare("
                UPDATE user_missions
                SET progress = :progress, completed = :completed
                WHERE id = :mission_id
            ");
            $update_stmt->execute([
                ':progress' => $new_progress,
                ':completed' => $completed,
                ':mission_id' => $mission['id']
            ]);
        }
    } catch (PDOException $e) {
        error_log("Error updating mission progress: " . $e->getMessage());
    }
}

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$increment = isset($data['increment']) ? (int)$data['increment'] : 0;

if ($increment > 0) {
    $user_id = $_SESSION['user_id'];
    $clan_id = isset($_SESSION['clan_id']) ? $_SESSION['clan_id'] : null;

    try {
        $pdo->beginTransaction();

        // Get user's current state (simplified query first)
        $stmt_get_user = $pdo->prepare("
            SELECT level, experience, level_up_threshold, prestige_points, money
            FROM users
            WHERE id = :user_id
        ");
        $stmt_get_user->execute([':user_id' => $user_id]);
        $user = $stmt_get_user->fetch(PDO::FETCH_ASSOC);

        // Debug user data retrieval
        error_log("Retrieved user data: " . json_encode($user));

        if (!$user) {
            throw new PDOException("User not found with ID: $user_id");
        }

        // Get click multiplier separately (for now, just use 1)
        $user['click_multiplier_sum'] = 0; // Simplified for debugging

        // Calculate bonuses
        $prestige_bonus = 1 + ($user['prestige_points'] * 0.01);
        $click_multiplier = $user['click_multiplier_sum'] > 0 ? $user['click_multiplier_sum'] : 1;
        $final_increment = floor($increment * $click_multiplier * $prestige_bonus);

        // Debug logging
        error_log("Update score debug - increment: $increment, click_multiplier: $click_multiplier, prestige_bonus: $prestige_bonus, final_increment: $final_increment");

        // Update user's stats, experience and money
        $new_experience = $user['experience'] + $final_increment;
        $new_money = $user['money'] + $final_increment;
        $level_up = false;
        $new_level = $user['level'];
        $new_level_up_threshold = $user['level_up_threshold'];

        // Debug logging
        error_log("Update score debug - old_money: {$user['money']}, new_money: $new_money, old_experience: {$user['experience']}, new_experience: $new_experience");

        if ($new_experience >= $user['level_up_threshold']) {
            $level_up = true;
            $new_level = $user['level'] + 1;
            $new_experience -= $user['level_up_threshold'];
            $new_level_up_threshold = floor($user['level_up_threshold'] * 1.5);
        }

        $stmt_update_user = $pdo->prepare("
            UPDATE users
            SET
                level = :new_level,
                experience = :new_experience,
                money = :new_money,
                merit = :new_money,
                level_up_threshold = :new_level_up_threshold,
                total_clicks = total_clicks + :increment,
                total_merit_earned = total_merit_earned + :final_increment
            WHERE id = :user_id;
        ");
        $update_result = $stmt_update_user->execute([
            ':new_level' => $new_level,
            ':new_experience' => $new_experience,
            ':new_money' => $new_money,
            ':new_level_up_threshold' => $new_level_up_threshold,
            ':increment' => $increment,
            ':final_increment' => $final_increment,
            ':user_id' => $user_id
        ]);

        // Check if the update actually affected any rows
        $rows_affected = $stmt_update_user->rowCount();
        error_log("Update user result: " . ($update_result ? 'true' : 'false') . ", rows affected: $rows_affected");

        if (!$update_result || $rows_affected === 0) {
            throw new PDOException("Failed to update user data - no rows affected");
        }

        // Update clan's total marks (only if user has a clan)
        if ($clan_id) {
            $stmt_update_clan = $pdo->prepare("
                UPDATE game_state
                SET total_marks = total_marks + :final_increment
                WHERE clan_id = :clan_id;
            ");
            $stmt_update_clan->execute([':final_increment' => $final_increment, ':clan_id' => $clan_id]);
        }

        // Update mission progress
        updateMissionProgress($pdo, $user_id, 'clicks', $increment);
        updateMissionProgress($pdo, $user_id, 'merit_earned', $final_increment);
        if ($level_up) {
            updateMissionProgress($pdo, $user_id, 'level_up', $new_level);
        }

        // Check for achievement unlocks
        $unlocked_achievements = [];
        // In a real scenario, you would check specific achievements based on the action
        // For now, we'll just return a generic message

        $pdo->commit();

        // Verify the update by reading back the values from database
        $verify_stmt = $pdo->prepare("SELECT money, merit, experience, level FROM users WHERE id = :user_id");
        $verify_stmt->execute([':user_id' => $user_id]);
        $verified_data = $verify_stmt->fetch(PDO::FETCH_ASSOC);

        error_log("Verified data after commit: " . json_encode($verified_data));

        echo json_encode([
            'success' => true,
            'experience' => $verified_data['experience'], // Use verified data
            'level_up_threshold' => $new_level_up_threshold,
            'money' => $verified_data['money'], // Use verified data
            'level_up' => $level_up,
            'new_level' => $level_up ? $verified_data['level'] : null, // Use verified data
            'unlocked_achievements' => $unlocked_achievements,
            'debug_calculated' => $new_money, // Show what we calculated
            'debug_verified' => $verified_data['money'] // Show what's actually in DB
        ]);

    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Update score error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'An error occurred while updating score.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid increment']);
}
?>