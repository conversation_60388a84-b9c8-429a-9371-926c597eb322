<?php
// public/api/update_score.php

session_start();

require_once 'db_connect.php';

// Debug: Log that the API was called
error_log("=== UPDATE_SCORE.PHP CALLED ===");

function updateMissionProgress($pdo, $user_id, $mission_type, $progress_amount) {
    try {
        // Get user missions of this type that are not completed
        $stmt = $pdo->prepare("
            SELECT um.id, m.target, um.progress
            FROM user_missions um
            JOIN missions m ON um.mission_id = m.id
            WHERE um.user_id = :user_id
            AND m.type = :mission_type
            AND um.completed = FALSE
            AND um.assigned_at = CURRENT_DATE
        ");
        $stmt->execute([':user_id' => $user_id, ':mission_type' => $mission_type]);
        $missions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($missions as $mission) {
            $new_progress = $mission['progress'] + $progress_amount;
            $completed = $new_progress >= $mission['target'];

            $update_stmt = $pdo->prepare("
                UPDATE user_missions
                SET progress = :progress, completed = :completed
                WHERE id = :mission_id
            ");
            $update_stmt->execute([
                ':progress' => $new_progress,
                ':completed' => $completed,
                ':mission_id' => $mission['id']
            ]);
        }
    } catch (PDOException $e) {
        error_log("Error updating mission progress: " . $e->getMessage());
    }
}

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    error_log("UPDATE_SCORE.PHP - User not logged in");
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);
$increment = isset($data['increment']) ? (int)$data['increment'] : 0;

// Debug input data
error_log("Raw input: " . $raw_input);
error_log("Parsed data: " . json_encode($data));
error_log("Increment value: " . $increment);
error_log("Increment > 0: " . ($increment > 0 ? 'true' : 'false'));

if ($increment > 0) {
    $user_id = $_SESSION['user_id'];
    $clan_id = isset($_SESSION['clan_id']) ? $_SESSION['clan_id'] : null;

    // Debug session data
    error_log("Session data - user_id: $user_id, clan_id: " . ($clan_id ?? 'null'));
    error_log("Full session: " . json_encode($_SESSION));

    try {
        $pdo->beginTransaction();

        // Get user's current state (use same simple query as working test)
        $stmt_get_user = $pdo->prepare("SELECT money, merit, experience, level, level_up_threshold, prestige_points FROM users WHERE id = ?");
        $stmt_get_user->execute([$user_id]);
        $user = $stmt_get_user->fetch(PDO::FETCH_ASSOC);

        // Debug user data retrieval
        error_log("Retrieved user data: " . json_encode($user));

        if (!$user) {
            throw new PDOException("User not found with ID: $user_id");
        }

        // Simplified calculation - just use the increment directly like the working test
        $final_increment = $increment;
        error_log("Using simplified calculation - final_increment: $final_increment");

        // Simplified calculation - removed for debugging
        // $prestige_bonus = 1 + ($user['prestige_points'] * 0.01);
        // $click_multiplier = $user['click_multiplier_sum'] > 0 ? $user['click_multiplier_sum'] : 1;
        // $final_increment = floor($increment * $click_multiplier * $prestige_bonus);

        // Debug logging
        error_log("Update score debug - increment: $increment, final_increment: $final_increment");

        // Update user's stats, experience and money
        $new_experience = $user['experience'] + $final_increment;
        $new_money = $user['money'] + $final_increment;
        $level_up = false;
        $new_level = $user['level'];
        $new_level_up_threshold = $user['level_up_threshold'];

        // Debug logging
        error_log("Update score debug - old_money: {$user['money']}, new_money: $new_money, old_experience: {$user['experience']}, new_experience: $new_experience");

        if ($new_experience >= $user['level_up_threshold']) {
            $level_up = true;
            $new_level = $user['level'] + 1;
            $new_experience -= $user['level_up_threshold'];
            $new_level_up_threshold = floor($user['level_up_threshold'] * 1.5);
        }

        // Use the same simple UPDATE as the working Simple API
        $stmt_update_user = $pdo->prepare("UPDATE users SET money = ?, experience = ?, total_clicks = total_clicks + ?, total_merit_earned = total_merit_earned + ? WHERE id = ?");
        $update_result = $stmt_update_user->execute([$new_money, $new_experience, $increment, $final_increment, $user_id]);

        error_log("Simple UPDATE executed with values: money=$new_money, experience=$new_experience, increment=$increment, final_increment=$final_increment, user_id=$user_id");

        // Check if the update actually affected any rows
        $rows_affected = $stmt_update_user->rowCount();
        error_log("Update user result: " . ($update_result ? 'true' : 'false') . ", rows affected: $rows_affected");

        // Check for SQL errors
        $error_info = $stmt_update_user->errorInfo();
        if ($error_info[0] !== '00000') {
            error_log("SQL Error: " . json_encode($error_info));
            throw new PDOException("SQL Error: " . $error_info[2]);
        }

        if (!$update_result || $rows_affected === 0) {
            throw new PDOException("Failed to update user data - no rows affected");
        }

        // Temporarily disabled for debugging
        // Update clan's total marks (only if user has a clan)
        // if ($clan_id) {
        //     $stmt_update_clan = $pdo->prepare("
        //         UPDATE game_state
        //         SET total_marks = total_marks + :final_increment
        //         WHERE clan_id = :clan_id;
        //     ");
        //     $stmt_update_clan->execute([':final_increment' => $final_increment, ':clan_id' => $clan_id]);
        // }

        // Update mission progress
        // updateMissionProgress($pdo, $user_id, 'clicks', $increment);
        // updateMissionProgress($pdo, $user_id, 'merit_earned', $final_increment);
        // if ($level_up) {
        //     updateMissionProgress($pdo, $user_id, 'level_up', $new_level);
        // }

        error_log("Skipped clan and mission updates for debugging");

        // Check for achievement unlocks
        $unlocked_achievements = [];
        // In a real scenario, you would check specific achievements based on the action
        // For now, we'll just return a generic message

        $pdo->commit();

        // Verify the update by reading back the values from database
        $verify_stmt = $pdo->prepare("SELECT money, experience, total_clicks, total_merit_earned FROM users WHERE id = ?");
        $verify_stmt->execute([$user_id]);
        $verified_data = $verify_stmt->fetch(PDO::FETCH_ASSOC);

        error_log("Verified data after commit: " . json_encode($verified_data));

        echo json_encode([
            'success' => true,
            'experience' => $verified_data['experience'], // Use verified data
            'level_up_threshold' => $user['level_up_threshold'], // Use original value since we didn't update it
            'money' => $verified_data['money'], // Use verified data
            'level_up' => false, // Simplified - no level up logic for now
            'new_level' => null,
            'unlocked_achievements' => [],
            'debug_calculated' => $new_money, // Show what we calculated
            'debug_verified' => $verified_data['money'], // Show what's actually in DB
            'debug_total_clicks' => $verified_data['total_clicks'],
            'debug_total_merit' => $verified_data['total_merit_earned']
        ]);

    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Update score error: " . $e->getMessage());
        error_log("Update score error trace: " . $e->getTraceAsString());
        echo json_encode(['success' => false, 'message' => 'An error occurred while updating score: ' . $e->getMessage()]);
    }
} else {
    error_log("Invalid increment - increment value: $increment, data: " . json_encode($data));
    echo json_encode(['success' => false, 'message' => 'Invalid increment', 'debug_increment' => $increment, 'debug_data' => $data]);
}
?>