<?php
// public/api/update_score.php

session_start();

require_once 'db_connect.php';

function updateMissionProgress($pdo, $user_id, $mission_type, $progress_amount) {
    try {
        // Get user missions of this type that are not completed
        $stmt = $pdo->prepare("
            SELECT um.id, m.target, um.progress
            FROM user_missions um
            JOIN missions m ON um.mission_id = m.id
            WHERE um.user_id = :user_id
            AND m.type = :mission_type
            AND um.completed = FALSE
            AND um.assigned_at = CURRENT_DATE
        ");
        $stmt->execute([':user_id' => $user_id, ':mission_type' => $mission_type]);
        $missions = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($missions as $mission) {
            $new_progress = $mission['progress'] + $progress_amount;
            $completed = $new_progress >= $mission['target'];

            $update_stmt = $pdo->prepare("
                UPDATE user_missions
                SET progress = :progress, completed = :completed
                WHERE id = :mission_id
            ");
            $update_stmt->execute([
                ':progress' => $new_progress,
                ':completed' => $completed,
                ':mission_id' => $mission['id']
            ]);
        }
    } catch (PDOException $e) {
        error_log("Error updating mission progress: " . $e->getMessage());
    }
}

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$increment = isset($data['increment']) ? (int)$data['increment'] : 0;

if ($increment > 0) {
    $user_id = $_SESSION['user_id'];
    $clan_id = isset($_SESSION['clan_id']) ? $_SESSION['clan_id'] : null;

    try {
        $pdo->beginTransaction();

        // Get user's current state and multipliers
        $stmt_get_user = $pdo->prepare("
            SELECT
                u.level, u.experience, u.level_up_threshold, u.prestige_points, u.money,
                COALESCE(SUM(CASE WHEN s.effect_type = 'click_multiplier' THEN s.effect_value ELSE 0 END), 0) as click_multiplier_sum
            FROM users u
            LEFT JOIN user_skills us ON u.id = us.user_id
            LEFT JOIN skills s ON us.skill_id = s.id
            WHERE u.id = :user_id
            GROUP BY u.id, u.level, u.experience, u.level_up_threshold, u.prestige_points, u.money;
        ");
        $stmt_get_user->execute([':user_id' => $user_id]);
        $user = $stmt_get_user->fetch(PDO::FETCH_ASSOC);

        // Calculate bonuses
        $prestige_bonus = 1 + ($user['prestige_points'] * 0.01);
        $click_multiplier = $user['click_multiplier_sum'] > 0 ? $user['click_multiplier_sum'] : 1;
        $final_increment = floor($increment * $click_multiplier * $prestige_bonus);

        // Debug logging
        error_log("Update score debug - increment: $increment, click_multiplier: $click_multiplier, prestige_bonus: $prestige_bonus, final_increment: $final_increment");

        // Update user's stats, experience and money
        $new_experience = $user['experience'] + $final_increment;
        $new_money = $user['money'] + $final_increment;
        $level_up = false;
        $new_level = $user['level'];
        $new_level_up_threshold = $user['level_up_threshold'];

        // Debug logging
        error_log("Update score debug - old_money: {$user['money']}, new_money: $new_money, old_experience: {$user['experience']}, new_experience: $new_experience");

        if ($new_experience >= $user['level_up_threshold']) {
            $level_up = true;
            $new_level = $user['level'] + 1;
            $new_experience -= $user['level_up_threshold'];
            $new_level_up_threshold = floor($user['level_up_threshold'] * 1.5);
        }

        $stmt_update_user = $pdo->prepare("
            UPDATE users
            SET
                level = :new_level,
                experience = :new_experience,
                money = :new_money,
                merit = :new_money,
                level_up_threshold = :new_level_up_threshold,
                total_clicks = total_clicks + :increment,
                total_merit_earned = total_merit_earned + :final_increment
            WHERE id = :user_id;
        ");
        $stmt_update_user->execute([
            ':new_level' => $new_level,
            ':new_experience' => $new_experience,
            ':new_money' => $new_money,
            ':new_level_up_threshold' => $new_level_up_threshold,
            ':increment' => $increment,
            ':final_increment' => $final_increment,
            ':user_id' => $user_id
        ]);

        // Update clan's total marks (only if user has a clan)
        if ($clan_id) {
            $stmt_update_clan = $pdo->prepare("
                UPDATE game_state
                SET total_marks = total_marks + :final_increment
                WHERE clan_id = :clan_id;
            ");
            $stmt_update_clan->execute([':final_increment' => $final_increment, ':clan_id' => $clan_id]);
        }

        // Update mission progress
        updateMissionProgress($pdo, $user_id, 'clicks', $increment);
        updateMissionProgress($pdo, $user_id, 'merit_earned', $final_increment);
        if ($level_up) {
            updateMissionProgress($pdo, $user_id, 'level_up', $new_level);
        }

        // Check for achievement unlocks
        $unlocked_achievements = [];
        // In a real scenario, you would check specific achievements based on the action
        // For now, we'll just return a generic message

        $pdo->commit();

        echo json_encode([
            'success' => true,
            'experience' => $new_experience,
            'level_up_threshold' => $new_level_up_threshold,
            'money' => $new_money,
            'level_up' => $level_up,
            'new_level' => $level_up ? $new_level : null,
            'unlocked_achievements' => $unlocked_achievements
        ]);

    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Update score error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'An error occurred while updating score.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid increment']);
}
?>