<?php
// public/api/get_buildings.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get all buildings
    $stmt_all_buildings = $pdo->query("SELECT * FROM buildings ORDER BY base_cost ASC;");
    $all_buildings = $stmt_all_buildings->fetchAll(PDO::FETCH_ASSOC);

    // Get user's building levels
    $stmt_user_buildings = $pdo->prepare("SELECT building_id, level FROM user_buildings WHERE user_id = :user_id;");
    $stmt_user_buildings->execute([':user_id' => $user_id]);
    $user_buildings_data = $stmt_user_buildings->fetchAll(PDO::FETCH_ASSOC);

    // Create a map of user's building levels for easy lookup
    $user_buildings = [];
    foreach ($user_buildings_data as $building) {
        $user_buildings[$building['building_id']] = $building['level'];
    }

    echo json_encode([
        'success' => true,
        'all_buildings' => $all_buildings,
        'user_buildings' => $user_buildings
    ]);

} catch (PDOException $e) {
    error_log("Get buildings error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while fetching building data.']);
}
?>