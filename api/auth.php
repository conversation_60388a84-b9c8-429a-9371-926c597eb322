<?php
header('Content-Type: application/json');
require_once 'db_connect.php';

$response = ['success' => false, 'message' => 'An unknown error occurred.'];

// Ensure $pdo is initialized and not null
if ($pdo === null) {
    $response['message'] = 'Database connection not established.';
    echo json_encode($response);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (isset($input['action'])) {
    switch ($input['action']) {
        case 'register':
            if (isset($input['username']) && isset($input['password'])) {
                $username = $input['username'];
                $password = $input['password'];

                // Enhanced input validation
                if (strlen($username) < 3 || strlen($username) > 50) {
                    $response['message'] = 'Username must be between 3 and 50 characters.';
                    break;
                }
                if (strlen($password) < 6 || strlen($password) > 255) {
                    $response['message'] = 'Password must be between 6 and 255 characters.';
                    break;
                }
                if (!preg_match('/^[a-zA-Z0-9_]+$/', $username)) {
                    $response['message'] = 'Username can only contain letters, numbers, and underscores.';
                    break;
                }

                $password_hash = password_hash($password, PASSWORD_DEFAULT);

                try {
                    $stmt = $pdo->prepare("INSERT INTO users (username, password_hash) VALUES (?, ?)");
                    if ($stmt->execute([$username, $password_hash])) {
                        $response['success'] = true;
                        $response['message'] = 'Registration successful!';
                    } else {
                        $response['message'] = 'Registration failed. Please try again.';
                    }
                } catch (PDOException $e) {
                    if ($e->getCode() == '23505') { // Unique violation error code for PostgreSQL
                        $response['message'] = 'Username already exists.';
                    } else {
                        error_log("Registration error: " . $e->getMessage());
                        $response['message'] = 'An error occurred during registration.';
                    }
                }
            } else {
                $response['message'] = 'Username and password are required for registration.';
            }
            break;

        case 'login':
            if (isset($input['username']) && isset($input['password'])) {
                $username = $input['username'];
                $password = $input['password'];

                try {
                    $stmt = $pdo->prepare("SELECT id, password_hash, clan_id FROM users WHERE username = ?");
                    $stmt->execute([$username]);
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($user && password_verify($password, $user['password_hash'])) {
                        // For simplicity, we're not using full session management here.
                        // In a real application, you'd generate a token and send it back.
                        $response['success'] = true;
                        $response['message'] = 'Login successful!';
                        $response['username'] = $username;
                        $response['clan'] = null;
                        if ($user['clan_id']) {
                            $clanStmt = $pdo->prepare("SELECT clan_name FROM clans WHERE id = ?");
                            $clanStmt->execute([$user['clan_id']]);
                            $clan = $clanStmt->fetch(PDO::FETCH_ASSOC);
                            if ($clan) {
                                $response['clan'] = $clan['clan_name'];
                            }
                        }
                    } else {
                        $response['message'] = 'Invalid username or password.';
                    }
                } catch (PDOException $e) {
                    error_log("Login error: " . $e->getMessage());
                    $response['message'] = 'An error occurred during login.';
                }
            } else {
                $response['message'] = 'Username and password are required for login.';
            }
            break;

        default:
            $response['message'] = 'Invalid action.';
            break;
    }
} else {
    $response['message'] = 'No action specified.';
}

echo json_encode($response);
?>