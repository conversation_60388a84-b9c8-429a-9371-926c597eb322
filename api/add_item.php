<?php
// api/add_item.php - Add item to user's inventory

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);

$user_id = $_SESSION['user_id'];
$item_id = isset($data['item_id']) ? (int)$data['item_id'] : 0;
$quantity = isset($data['quantity']) ? (int)$data['quantity'] : 1;

if ($item_id <= 0 || $quantity <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid item ID or quantity']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Get item details
    $itemStmt = $pdo->prepare("SELECT * FROM items WHERE id = :item_id");
    $itemStmt->execute([':item_id' => $item_id]);
    $item = $itemStmt->fetch(PDO::FETCH_ASSOC);

    if (!$item) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Item not found']);
        exit;
    }

    // Check if user already has this item
    $existingStmt = $pdo->prepare("SELECT * FROM user_inventory WHERE user_id = :user_id AND item_id = :item_id");
    $existingStmt->execute([':user_id' => $user_id, ':item_id' => $item_id]);
    $existing = $existingStmt->fetch(PDO::FETCH_ASSOC);

    if ($existing) {
        // Update existing item quantity (check max stack)
        $new_quantity = $existing['quantity'] + $quantity;
        if ($new_quantity > $item['max_stack']) {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => 'Cannot exceed max stack size']);
            exit;
        }

        $updateStmt = $pdo->prepare("UPDATE user_inventory SET quantity = :quantity WHERE id = :id");
        $updateStmt->execute([':quantity' => $new_quantity, ':id' => $existing['id']]);
    } else {
        // Add new item to inventory
        if ($quantity > $item['max_stack']) {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => 'Quantity exceeds max stack size']);
            exit;
        }

        $insertStmt = $pdo->prepare("
            INSERT INTO user_inventory (user_id, item_id, quantity, obtained_at) 
            VALUES (:user_id, :item_id, :quantity, NOW())
        ");
        $insertStmt->execute([
            ':user_id' => $user_id,
            ':item_id' => $item_id,
            ':quantity' => $quantity
        ]);
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => "獲得 {$item['name']} x{$quantity}",
        'item' => $item,
        'quantity_added' => $quantity
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Add item error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
