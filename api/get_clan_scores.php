<?php
// public/api/get_clan_scores.php

require_once 'db_connect.php';

header('Content-Type: application/json');

try {
    $stmt_dashboard = $pdo->query("
        SELECT c.clan_name AS name, gs.total_marks
        FROM game_state gs
        JOIN clans c ON gs.clan_id = c.id
        ORDER BY gs.total_marks DESC;
    ");
    $dashboard_data = $stmt_dashboard->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode(['success' => true, 'scores' => $dashboard_data]);

} catch (PDOException $e) {
    error_log("Get clan scores error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while fetching clan scores.']);
}
?>