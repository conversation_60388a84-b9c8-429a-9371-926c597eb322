<?php
// public/api/get_clan_scores.php

require_once 'db_connect.php';

header('Content-Type: application/json');
header('Cache-Control: no-cache, no-store, must-revalidate');
header('Pragma: no-cache');
header('Expires: 0');

// Debug logging
error_log("=== GET_CLAN_SCORES.PHP CALLED ===");

try {
    $stmt_dashboard = $pdo->query("
        SELECT c.clan_name AS name, gs.total_marks
        FROM game_state gs
        JOIN clans c ON gs.clan_id = c.id
        ORDER BY gs.total_marks DESC;
    ");
    $dashboard_data = $stmt_dashboard->fetchAll(PDO::FETCH_ASSOC);

    // Debug logging
    error_log("Clan scores retrieved: " . count($dashboard_data) . " clans");
    error_log("Top clan data: " . json_encode($dashboard_data[0] ?? 'No data'));

    echo json_encode(['success' => true, 'scores' => $dashboard_data, 'timestamp' => time()]);

} catch (PDOException $e) {
    error_log("Get clan scores error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while fetching clan scores.']);
}
?>