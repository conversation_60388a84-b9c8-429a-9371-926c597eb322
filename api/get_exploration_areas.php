<?php
// api/get_exploration_areas.php - Get exploration areas and user's progress

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get user level and energy
    $userStmt = $pdo->prepare("
        SELECT u.level, ue.current_energy, ue.max_energy, ue.last_regeneration
        FROM users u
        LEFT JOIN user_energy ue ON u.id = ue.user_id
        WHERE u.id = :user_id
    ");
    $userStmt->execute([':user_id' => $user_id]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    // Initialize energy if not exists
    if (!$user['current_energy']) {
        $initEnergyStmt = $pdo->prepare("
            INSERT INTO user_energy (user_id, current_energy, max_energy, last_regeneration)
            VALUES (:user_id, 100, 100, NOW())
            ON CONFLICT (user_id) DO NOTHING
        ");
        $initEnergyStmt->execute([':user_id' => $user_id]);
        $user['current_energy'] = 100;
        $user['max_energy'] = 100;
    }

    // Regenerate energy (1 energy per minute)
    if ($user['last_regeneration']) {
        $lastRegen = new DateTime($user['last_regeneration']);
        $now = new DateTime();
        $minutesPassed = floor(($now->getTimestamp() - $lastRegen->getTimestamp()) / 60);
        
        if ($minutesPassed > 0) {
            $newEnergy = min($user['max_energy'], $user['current_energy'] + $minutesPassed);
            $updateEnergyStmt = $pdo->prepare("
                UPDATE user_energy 
                SET current_energy = :energy, last_regeneration = NOW()
                WHERE user_id = :user_id
            ");
            $updateEnergyStmt->execute([':energy' => $newEnergy, ':user_id' => $user_id]);
            $user['current_energy'] = $newEnergy;
        }
    }

    // Get all exploration areas
    $areasStmt = $pdo->query("
        SELECT * FROM exploration_areas 
        ORDER BY unlock_order ASC, required_level ASC
    ");
    $areas = $areasStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get rewards for each area
    $areasWithRewards = [];
    foreach ($areas as $area) {
        $rewardsStmt = $pdo->prepare("
            SELECT er.*, i.name as item_name, i.icon as item_icon, i.rarity as item_rarity
            FROM exploration_rewards er
            LEFT JOIN items i ON er.item_id = i.id
            WHERE er.area_id = :area_id
            ORDER BY er.drop_rate DESC
        ");
        $rewardsStmt->execute([':area_id' => $area['id']]);
        $rewards = $rewardsStmt->fetchAll(PDO::FETCH_ASSOC);

        $area['rewards'] = $rewards;
        $area['is_unlocked'] = $user['level'] >= $area['required_level'];
        $area['can_explore'] = $area['is_unlocked'] && ($user['current_energy'] >= $area['energy_cost']);
        
        $areasWithRewards[] = $area;
    }

    // Get user's current exploration progress
    $progressStmt = $pdo->prepare("
        SELECT uep.*, ea.name as area_name, ea.exploration_time_seconds,
               ea.background_image, ea.energy_cost
        FROM user_exploration_progress uep
        JOIN exploration_areas ea ON uep.area_id = ea.id
        WHERE uep.user_id = :user_id AND uep.is_completed = false
        ORDER BY uep.started_at DESC
    ");
    $progressStmt->execute([':user_id' => $user_id]);
    $currentProgress = $progressStmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate remaining time for each progress
    foreach ($currentProgress as &$progress) {
        $completionTime = new DateTime($progress['completion_time']);
        $now = new DateTime();
        $remainingSeconds = max(0, $completionTime->getTimestamp() - $now->getTimestamp());
        $progress['remaining_seconds'] = $remainingSeconds;
        $progress['is_ready'] = $remainingSeconds <= 0;
    }

    echo json_encode([
        'success' => true,
        'areas' => $areasWithRewards,
        'current_progress' => $currentProgress,
        'user_energy' => [
            'current' => $user['current_energy'],
            'max' => $user['max_energy']
        ],
        'user_level' => $user['level']
    ]);

} catch (PDOException $e) {
    error_log("Get exploration areas error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
