<?php
// public/api/buy_shop_item.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$item_id = isset($data['item_id']) ? (int)$data['item_id'] : 0;

if ($item_id > 0) {
    $user_id = $_SESSION['user_id'];

    // Get item details
    $stmt_item = $pdo->prepare("SELECT * FROM shop_items WHERE id = :item_id;");
    $stmt_item->execute([':item_id' => $item_id]);
    $item = $stmt_item->fetch(PDO::FETCH_ASSOC);

    // Get user's money
    $stmt_user = $pdo->prepare("SELECT money FROM users WHERE id = :user_id;");
    $stmt_user->execute([':user_id' => $user_id]);
    $user = $stmt_user->fetch(PDO::FETCH_ASSOC);

    if ($item && $user['money'] >= $item['cost']) {
        // Deduct cost
        $new_money = $user['money'] - $item['cost'];
        $stmt_update_money = $pdo->prepare("UPDATE users SET money = :new_money WHERE id = :user_id;");
        $stmt_update_money->execute([':new_money' => $new_money, ':user_id' => $user_id]);

        // Add item to user with expiration
        $expires_at = null;
        if ($item['duration_seconds']) {
            $expires_at = date('Y-m-d H:i:s O', time() + $item['duration_seconds']);
        }
        
        $stmt_add_item = $pdo->prepare("INSERT INTO user_items (user_id, item_id, expires_at) VALUES (:user_id, :item_id, :expires_at);");
        $stmt_add_item->execute([':user_id' => $user_id, ':item_id' => $item_id, ':expires_at' => $expires_at]);
        
        echo json_encode(['success' => true, 'message' => 'Item purchased successfully', 'new_money' => $new_money]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Not enough money or item not found']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid item ID']);
}
?>