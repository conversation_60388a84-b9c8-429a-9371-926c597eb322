<?php

// Load environment variables from .env file using a simple parser
// Note: This is a basic parser. For production, consider a more robust library like vlucas/phpdotenv.
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
}

$host = getenv('DB_HOST') ?: 'pgsql5.serv00.com';
$dbname = getenv('DB_NAME') ?: 'p4481_mokugyo';
$user = getenv('DB_USER') ?: 'p4481_mokugyo';
$password = getenv('DB_PASSWORD') ?: '1qaz@WSX';
$port = getenv('DB_PORT') ?: '5432';

$pdo = null;

try {
    $dsn = "pgsql:host=$host;port=$port;dbname=$dbname;";
    $pdo = new PDO($dsn, $user, $password, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    // echo "Connected successfully to PostgreSQL!"; // For debugging
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    // echo "Connection failed: " . $e->getMessage(); // For debugging
}

function initializeDatabase($pdo) {
    if ($pdo === null) {
        error_log("PDO object is null in initializeDatabase. Cannot initialize database.");
        return;
    }

    try {
        // Create users table
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            clan_id INTEGER DEFAULT NULL,
            merit BIGINT DEFAULT 0,
            level INTEGER DEFAULT 1,
            experience BIGINT DEFAULT 0,
            money BIGINT DEFAULT 0,
            level_up_threshold BIGINT DEFAULT 100,
            prestige_points INTEGER DEFAULT 0,
            last_passive_income_collection TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_clicks BIGINT DEFAULT 0,
            total_merit_earned BIGINT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );");

        // Create clans table
        $pdo->exec("CREATE TABLE IF NOT EXISTS clans (
            id SERIAL PRIMARY KEY,
            clan_name VARCHAR(255) UNIQUE NOT NULL,
            total_merit BIGINT DEFAULT 0
        );");

        // Create user_sessions table (for persistent login, if needed later)
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_sessions (
            session_id VARCHAR(255) PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            expires_at TIMESTAMP NOT NULL
        );");

        // Create game_state table
        $pdo->exec("CREATE TABLE IF NOT EXISTS game_state (
            clan_id INTEGER PRIMARY KEY REFERENCES clans(id) ON DELETE CASCADE,
            total_marks BIGINT DEFAULT 0
        );");

        // Pre-populate clans table with Hong Kong's 18 districts if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM clans;");
        if ($stmt->fetchColumn() == 0) {
            $districts = [
                '中西區', '灣仔區', '東區', '南區',
                '油尖旺區', '深水埗區', '九龍城區', '黃大仙區', '觀塘區',
                '葵青區', '荃灣區', '屯門區', '元朗區', '北區', '大埔區', '沙田區', '西貢區',
                '離島區'
            ];
            $insertClanStmt = $pdo->prepare("INSERT INTO clans (clan_name) VALUES (?) RETURNING id;");
            $insertGameStateStmt = $pdo->prepare("INSERT INTO game_state (clan_id, total_marks) VALUES (?, 0);");
            foreach ($districts as $district) {
                $insertClanStmt->execute([$district]);
                $clanId = $insertClanStmt->fetchColumn();
                $insertGameStateStmt->execute([$clanId]);
            }
        }

        // Create skills table (Upgrades)
        $pdo->exec("CREATE TABLE IF NOT EXISTS skills (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            cost BIGINT NOT NULL,
            effect_type VARCHAR(50) NOT NULL, -- e.g., 'click_multiplier', 'passive_income_multiplier'
            effect_value FLOAT NOT NULL,
            required_building_id INTEGER DEFAULT NULL, -- Link upgrades to buildings
            required_building_level INTEGER DEFAULT NULL
        );");

        // Pre-populate skills if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM skills;");
        if ($stmt->fetchColumn() == 0) {
            $skills = [
                ['虔誠信徒', '點擊功德獲取量 x2', 1000, 'click_multiplier', 2, null, null],
                ['木魚加持', '所有建築的功德產出量 +10%', 5000, 'passive_income_multiplier', 1.1, null, null],
                ['金剛經', '點擊功德獲取量 x3', 10000, 'click_multiplier', 3, null, null],
                ['舍利子', '所有建築的功德產出量 +20%', 50000, 'passive_income_multiplier', 1.2, null, null]
            ];
            $insertSkillStmt = $pdo->prepare("INSERT INTO skills (name, description, cost, effect_type, effect_value, required_building_id, required_building_level) VALUES (?, ?, ?, ?, ?, ?, ?);");
            foreach ($skills as $skill) {
                $insertSkillStmt->execute($skill);
            }
        }

        // Create shop_items table (Boosts)
        $pdo->exec("CREATE TABLE IF NOT EXISTS shop_items (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            cost INTEGER NOT NULL,
            duration_seconds INTEGER,
            effect_type VARCHAR(50) NOT NULL, -- e.g., 'temp_click_boost', 'temp_passive_boost'
            effect_value FLOAT NOT NULL
        );");

        // Pre-populate shop_items if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM shop_items;");
        if ($stmt->fetchColumn() == 0) {
            $items = [
                ['功德加倍符', '60秒內點擊功德獲取量 x7', 50, 60, 'temp_click_boost', 7],
                ['財源滾滾', '300秒內所有建築功德產出量 x5', 100, 300, 'temp_passive_boost', 5]
            ];
            $insertItemStmt = $pdo->prepare("INSERT INTO shop_items (name, description, cost, duration_seconds, effect_type, effect_value) VALUES (?, ?, ?, ?, ?, ?);");
            foreach ($items as $item) {
                $insertItemStmt->execute($item);
            }
        }

        // Create missions table
        $pdo->exec("CREATE TABLE IF NOT EXISTS missions (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            type VARCHAR(50) NOT NULL, -- e.g., 'clicks', 'merit_earned', 'level_up'
            target BIGINT NOT NULL,
            reward_money BIGINT DEFAULT 0,
            reward_experience BIGINT DEFAULT 0
        );");

        // Pre-populate missions if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM missions;");
        if ($stmt->fetchColumn() == 0) {
            $missions = [
                ['初級念佛', '點擊木魚 1,000 次', 'clicks', 1000, 500, 200],
                ['功德無量', '賺取 10,000 功德', 'merit_earned', 10000, 1000, 500],
                ['初窺門徑', '達到等級 10', 'level_up', 10, 2000, 1000]
            ];
            $insertMissionStmt = $pdo->prepare("INSERT INTO missions (name, description, type, target, reward_money, reward_experience) VALUES (?, ?, ?, ?, ?, ?);");
            foreach ($missions as $mission) {
                $insertMissionStmt->execute($mission);
            }
        }

        // Create buildings table
        $pdo->exec("CREATE TABLE IF NOT EXISTS buildings (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            base_cost BIGINT NOT NULL,
            base_production FLOAT NOT NULL,
            cost_increase_ratio FLOAT NOT NULL
        );");

        // Pre-populate buildings if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM buildings;");
        if ($stmt->fetchColumn() == 0) {
            $buildings = [
                ['蒲團', '一個簡單的蒲團，用於打坐。', 15, 0.1, 1.15],
                ['佛經', '一本佛經，可以增加你的智慧。', 100, 1, 1.15],
                ['佛堂', '一個小佛堂，可以容納更多信徒。', 1100, 8, 1.15],
                ['寺廟', '一座宏偉的寺廟，可以吸引更多信徒。', 12000, 47, 1.15],
                ['佛塔', '一座高聳的佛塔，可以傳播你的信仰。', 130000, 260, 1.15]
            ];
            $insertBuildingStmt = $pdo->prepare("INSERT INTO buildings (name, description, base_cost, base_production, cost_increase_ratio) VALUES (?, ?, ?, ?, ?);");
            foreach ($buildings as $building) {
                $insertBuildingStmt->execute($building);
            }
        }

        // Create user_buildings table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_buildings (
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            building_id INTEGER NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
            level INTEGER DEFAULT 0,
            PRIMARY KEY (user_id, building_id)
        );");

        // Create achievements table
        $pdo->exec("CREATE TABLE IF NOT EXISTS achievements (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            type VARCHAR(50) NOT NULL, -- e.g., 'total_clicks', 'total_merit_earned', 'building_level'
            target BIGINT NOT NULL,
            building_id_requirement INTEGER DEFAULT NULL -- For building-specific achievements
        );");

        // Pre-populate achievements if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM achievements;");
        if ($stmt->fetchColumn() == 0) {
            $achievements = [
                ['手速達人', '總點擊次數達到 10,000', 'total_clicks', 10000, null],
                ['功德圓滿', '總功德獲取量達到 1,000,000', 'total_merit_earned', 1000000, null],
                ['建築大師', '擁有一個 10 級的寺廟', 'building_level', 10, 4]
            ];
            $insertAchievementStmt = $pdo->prepare("INSERT INTO achievements (name, description, type, target, building_id_requirement) VALUES (?, ?, ?, ?, ?);");
            foreach ($achievements as $achievement) {
                $insertAchievementStmt->execute($achievement);
            }
        }

        // Create user_achievements table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_achievements (
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            achievement_id INTEGER NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
            unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, achievement_id)
        );");

        // Create user_skills table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_skills (
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            skill_id INTEGER NOT NULL REFERENCES skills(id) ON DELETE CASCADE,
            PRIMARY KEY (user_id, skill_id)
        );");

        // Create user_items table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_items (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            item_id INTEGER NOT NULL REFERENCES shop_items(id) ON DELETE CASCADE,
            expires_at TIMESTAMP NULL
        );");

        // Create user_missions table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_missions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            mission_id INTEGER NOT NULL REFERENCES missions(id) ON DELETE CASCADE,
            progress INTEGER DEFAULT 0,
            completed BOOLEAN DEFAULT FALSE,
            assigned_at DATE DEFAULT CURRENT_DATE,
            UNIQUE (user_id, mission_id, assigned_at) -- Ensure a user gets each mission type once per day
        );");

        // === NEW FEATURES DATABASE TABLES ===

        // 1. INVENTORY SYSTEM
        // Items table - defines all possible items in the game
        $pdo->exec("CREATE TABLE IF NOT EXISTS items (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            type VARCHAR(50) NOT NULL, -- 'material', 'equipment', 'consumable', 'treasure'
            rarity VARCHAR(20) NOT NULL DEFAULT 'common', -- 'common', 'uncommon', 'rare', 'epic', 'legendary'
            max_stack INTEGER DEFAULT 1, -- How many can stack in one slot
            sell_price BIGINT DEFAULT 0,
            icon VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );");

        // User inventory - stores user's items
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_inventory (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            item_id INTEGER NOT NULL REFERENCES items(id) ON DELETE CASCADE,
            quantity INTEGER DEFAULT 1,
            slot_position INTEGER DEFAULT NULL, -- For organized inventory
            obtained_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(user_id, item_id) -- One entry per item type per user
        );");

        // 2. GACHA SYSTEM
        // Gacha pools - different lottery pools
        $pdo->exec("CREATE TABLE IF NOT EXISTS gacha_pools (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            cost_type VARCHAR(50) NOT NULL DEFAULT 'money', -- 'money', 'gems', 'tickets'
            cost_amount INTEGER NOT NULL,
            is_active BOOLEAN DEFAULT TRUE,
            banner_image VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );");

        // Gacha pool items - what can be obtained from each pool
        $pdo->exec("CREATE TABLE IF NOT EXISTS gacha_pool_items (
            id SERIAL PRIMARY KEY,
            pool_id INTEGER NOT NULL REFERENCES gacha_pools(id) ON DELETE CASCADE,
            item_id INTEGER NOT NULL REFERENCES items(id) ON DELETE CASCADE,
            drop_rate DECIMAL(5,4) NOT NULL, -- 0.0001 to 1.0000 (0.01% to 100%)
            quantity_min INTEGER DEFAULT 1,
            quantity_max INTEGER DEFAULT 1,
            is_featured BOOLEAN DEFAULT FALSE
        );");

        // User gacha history
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_gacha_history (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            pool_id INTEGER NOT NULL REFERENCES gacha_pools(id) ON DELETE CASCADE,
            item_id INTEGER NOT NULL REFERENCES items(id) ON DELETE CASCADE,
            quantity INTEGER DEFAULT 1,
            pulled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );");

        // 3. ALCHEMY SYSTEM
        // Alchemy recipes - crafting recipes
        $pdo->exec("CREATE TABLE IF NOT EXISTS alchemy_recipes (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            result_item_id INTEGER NOT NULL REFERENCES items(id) ON DELETE CASCADE,
            result_quantity INTEGER DEFAULT 1,
            craft_time_seconds INTEGER DEFAULT 0, -- Time to craft
            required_level INTEGER DEFAULT 1,
            success_rate DECIMAL(3,2) DEFAULT 1.00, -- 0.00 to 1.00
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );");

        // Recipe materials - what materials are needed for each recipe
        $pdo->exec("CREATE TABLE IF NOT EXISTS recipe_materials (
            id SERIAL PRIMARY KEY,
            recipe_id INTEGER NOT NULL REFERENCES alchemy_recipes(id) ON DELETE CASCADE,
            material_item_id INTEGER NOT NULL REFERENCES items(id) ON DELETE CASCADE,
            quantity_required INTEGER NOT NULL
        );");

        // User alchemy progress - ongoing crafting
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_alchemy_progress (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            recipe_id INTEGER NOT NULL REFERENCES alchemy_recipes(id) ON DELETE CASCADE,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completion_time TIMESTAMP NOT NULL,
            is_completed BOOLEAN DEFAULT FALSE
        );");

        // 4. EXPLORATION SYSTEM
        // Exploration areas - different locations to explore
        $pdo->exec("CREATE TABLE IF NOT EXISTS exploration_areas (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            required_level INTEGER DEFAULT 1,
            exploration_time_seconds INTEGER DEFAULT 3600, -- 1 hour default
            energy_cost INTEGER DEFAULT 10,
            background_image VARCHAR(255) DEFAULT NULL,
            unlock_order INTEGER DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );");

        // Area rewards - what can be found in each area
        $pdo->exec("CREATE TABLE IF NOT EXISTS exploration_rewards (
            id SERIAL PRIMARY KEY,
            area_id INTEGER NOT NULL REFERENCES exploration_areas(id) ON DELETE CASCADE,
            reward_type VARCHAR(50) NOT NULL, -- 'item', 'money', 'experience'
            item_id INTEGER DEFAULT NULL REFERENCES items(id) ON DELETE CASCADE,
            money_amount BIGINT DEFAULT 0,
            experience_amount BIGINT DEFAULT 0,
            drop_rate DECIMAL(3,2) NOT NULL, -- 0.00 to 1.00
            quantity_min INTEGER DEFAULT 1,
            quantity_max INTEGER DEFAULT 1
        );");

        // User exploration progress
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_exploration_progress (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            area_id INTEGER NOT NULL REFERENCES exploration_areas(id) ON DELETE CASCADE,
            started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            completion_time TIMESTAMP NOT NULL,
            is_completed BOOLEAN DEFAULT FALSE,
            energy_spent INTEGER DEFAULT 0
        );");

        // 5. DUNGEON SYSTEM
        // Dungeons - different dungeon types
        $pdo->exec("CREATE TABLE IF NOT EXISTS dungeons (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            required_level INTEGER DEFAULT 1,
            max_floors INTEGER DEFAULT 10,
            energy_cost_per_floor INTEGER DEFAULT 5,
            reset_type VARCHAR(20) DEFAULT 'daily', -- 'daily', 'weekly', 'never'
            background_image VARCHAR(255) DEFAULT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );");

        // Dungeon floors - individual floors with enemies and rewards
        $pdo->exec("CREATE TABLE IF NOT EXISTS dungeon_floors (
            id SERIAL PRIMARY KEY,
            dungeon_id INTEGER NOT NULL REFERENCES dungeons(id) ON DELETE CASCADE,
            floor_number INTEGER NOT NULL,
            enemy_name VARCHAR(255) NOT NULL,
            enemy_hp BIGINT NOT NULL,
            enemy_attack BIGINT NOT NULL,
            enemy_defense BIGINT DEFAULT 0,
            boss_floor BOOLEAN DEFAULT FALSE,
            UNIQUE(dungeon_id, floor_number)
        );");

        // Dungeon floor rewards
        $pdo->exec("CREATE TABLE IF NOT EXISTS dungeon_floor_rewards (
            id SERIAL PRIMARY KEY,
            floor_id INTEGER NOT NULL REFERENCES dungeon_floors(id) ON DELETE CASCADE,
            reward_type VARCHAR(50) NOT NULL, -- 'item', 'money', 'experience'
            item_id INTEGER DEFAULT NULL REFERENCES items(id) ON DELETE CASCADE,
            money_amount BIGINT DEFAULT 0,
            experience_amount BIGINT DEFAULT 0,
            drop_rate DECIMAL(3,2) NOT NULL, -- 0.00 to 1.00
            quantity_min INTEGER DEFAULT 1,
            quantity_max INTEGER DEFAULT 1,
            first_clear_only BOOLEAN DEFAULT FALSE
        );");

        // User dungeon progress
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_dungeon_progress (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            dungeon_id INTEGER NOT NULL REFERENCES dungeons(id) ON DELETE CASCADE,
            highest_floor_cleared INTEGER DEFAULT 0,
            last_reset_date DATE DEFAULT CURRENT_DATE,
            total_clears INTEGER DEFAULT 0,
            UNIQUE(user_id, dungeon_id)
        );");

        // User energy system
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_energy (
            user_id INTEGER PRIMARY KEY REFERENCES users(id) ON DELETE CASCADE,
            current_energy INTEGER DEFAULT 100,
            max_energy INTEGER DEFAULT 100,
            last_regeneration TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            energy_regen_rate INTEGER DEFAULT 1 -- energy per minute
        );");

        // === POPULATE INITIAL DATA FOR NEW FEATURES ===

        // Populate items table if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM items;");
        if ($stmt->fetchColumn() == 0) {
            $items = [
                // Materials
                ['靈草', '普通的修煉材料', 'material', 'common', 99, 10, 'herb.png'],
                ['靈石', '蘊含靈氣的石頭', 'material', 'uncommon', 50, 50, 'spirit_stone.png'],
                ['天材地寶', '稀有的修煉材料', 'material', 'rare', 20, 200, 'treasure.png'],
                ['龍血草', '傳說中的神草', 'material', 'epic', 5, 1000, 'dragon_herb.png'],

                // Equipment
                ['木劍', '初學者的武器', 'equipment', 'common', 1, 100, 'wooden_sword.png'],
                ['鐵劍', '普通的鐵製劍', 'equipment', 'uncommon', 1, 500, 'iron_sword.png'],
                ['靈劍', '蘊含靈氣的劍', 'equipment', 'rare', 1, 2000, 'spirit_sword.png'],
                ['神劍', '傳說中的神器', 'equipment', 'legendary', 1, 10000, 'divine_sword.png'],

                // Consumables
                ['回復丹', '恢復體力的丹藥', 'consumable', 'common', 10, 20, 'healing_pill.png'],
                ['靈氣丹', '增加修為的丹藥', 'consumable', 'uncommon', 5, 100, 'spirit_pill.png'],
                ['突破丹', '幫助突破境界的丹藥', 'consumable', 'rare', 3, 500, 'breakthrough_pill.png']
            ];
            $insertItemStmt = $pdo->prepare("INSERT INTO items (name, description, type, rarity, max_stack, sell_price, icon) VALUES (?, ?, ?, ?, ?, ?, ?);");
            foreach ($items as $item) {
                $insertItemStmt->execute($item);
            }
        }

        // Populate gacha pools if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM gacha_pools;");
        if ($stmt->fetchColumn() == 0) {
            $pools = [
                ['基礎抽獎', '使用功德進行基礎抽獎', 'money', 1000, true, 'basic_banner.png'],
                ['高級抽獎', '使用功德進行高級抽獎，更高機率獲得稀有物品', 'money', 5000, true, 'premium_banner.png']
            ];
            $insertPoolStmt = $pdo->prepare("INSERT INTO gacha_pools (name, description, cost_type, cost_amount, is_active, banner_image) VALUES (?, ?, ?, ?, ?, ?);");
            foreach ($pools as $pool) {
                $insertPoolStmt->execute($pool);
            }
        }

        // Populate exploration areas if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM exploration_areas;");
        if ($stmt->fetchColumn() == 0) {
            $areas = [
                ['後山小徑', '寺廟後山的小路，適合初學者探索', 1, 1800, 10, 'mountain_path.png', 1],
                ['竹林深處', '幽靜的竹林，隱藏著不少寶物', 5, 3600, 15, 'bamboo_forest.png', 2],
                ['靈泉洞穴', '充滿靈氣的洞穴，危險但回報豐厚', 10, 7200, 25, 'spirit_cave.png', 3],
                ['雲霧山峰', '高聳入雲的山峰，只有高手才能到達', 20, 14400, 40, 'misty_peak.png', 4]
            ];
            $insertAreaStmt = $pdo->prepare("INSERT INTO exploration_areas (name, description, required_level, exploration_time_seconds, energy_cost, background_image, unlock_order) VALUES (?, ?, ?, ?, ?, ?, ?);");
            foreach ($areas as $area) {
                $insertAreaStmt->execute($area);
            }
        }

        // Populate dungeons if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM dungeons;");
        if ($stmt->fetchColumn() == 0) {
            $dungeons = [
                ['試煉之塔', '測試修行者實力的古老高塔', 1, 10, 5, 'daily', 'trial_tower.png'],
                ['魔獸洞窟', '充滿危險魔獸的地下洞窟', 10, 15, 8, 'daily', 'beast_cave.png'],
                ['禁忌秘境', '傳說中的禁地，蘊藏著巨大的危險與機遇', 25, 20, 12, 'weekly', 'forbidden_realm.png']
            ];
            $insertDungeonStmt = $pdo->prepare("INSERT INTO dungeons (name, description, required_level, max_floors, energy_cost_per_floor, reset_type, background_image) VALUES (?, ?, ?, ?, ?, ?, ?);");
            foreach ($dungeons as $dungeon) {
                $insertDungeonStmt->execute($dungeon);
            }
        }

        // Populate alchemy recipes if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM alchemy_recipes;");
        if ($stmt->fetchColumn() == 0) {
            // First, get item IDs for recipes
            $itemIds = [];
            $itemStmt = $pdo->query("SELECT id, name FROM items;");
            while ($row = $itemStmt->fetch(PDO::FETCH_ASSOC)) {
                $itemIds[$row['name']] = $row['id'];
            }

            if (!empty($itemIds)) {
                $recipes = [
                    ['煉製回復丹', '使用靈草煉製回復丹', $itemIds['回復丹'] ?? 1, 1, 300, 1, 0.90],
                    ['煉製靈氣丹', '使用靈石煉製靈氣丹', $itemIds['靈氣丹'] ?? 2, 1, 1800, 5, 0.80],
                    ['煉製突破丹', '使用天材地寶煉製突破丹', $itemIds['突破丹'] ?? 3, 1, 7200, 15, 0.60]
                ];
                $insertRecipeStmt = $pdo->prepare("INSERT INTO alchemy_recipes (name, description, result_item_id, result_quantity, craft_time_seconds, required_level, success_rate) VALUES (?, ?, ?, ?, ?, ?, ?);");
                foreach ($recipes as $recipe) {
                    $insertRecipeStmt->execute($recipe);
                }
            }
        }

        echo "Database initialized successfully with new features!"; // For debugging
    } catch (PDOException $e) {
        error_log("Database initialization failed: " . $e->getMessage());
        // echo "Database initialization failed: " . $e->getMessage(); // For debugging
    }
}

// Ensure initializeDatabase is called only if $pdo is successfully connected
if ($pdo) {
    initializeDatabase($pdo);
}

?>
