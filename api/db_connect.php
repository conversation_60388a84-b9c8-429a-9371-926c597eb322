<?php

// Load environment variables from .env file using a simple parser
// Note: This is a basic parser. For production, consider a more robust library like vlucas/phpdotenv.
if (file_exists(__DIR__ . '/../.env')) {
    $lines = file(__DIR__ . '/../.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos(trim($line), '#') === 0) {
            continue;
        }
        list($name, $value) = explode('=', $line, 2);
        $name = trim($name);
        $value = trim($value);
        if (!array_key_exists($name, $_SERVER) && !array_key_exists($name, $_ENV)) {
            putenv(sprintf('%s=%s', $name, $value));
            $_ENV[$name] = $value;
            $_SERVER[$name] = $value;
        }
    }
}

$host = getenv('DB_HOST') ?: 'pgsql5.serv00.com';
$dbname = getenv('DB_NAME') ?: 'p4481_mokugyo';
$user = getenv('DB_USER') ?: 'p4481_mokugyo';
$password = getenv('DB_PASSWORD') ?: '1qaz@WSX';
$port = getenv('DB_PORT') ?: '5432';

$pdo = null;

try {
    $dsn = "pgsql:host=$host;port=$port;dbname=$dbname;";
    $pdo = new PDO($dsn, $user, $password, [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
    // echo "Connected successfully to PostgreSQL!"; // For debugging
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    // echo "Connection failed: " . $e->getMessage(); // For debugging
}

function initializeDatabase($pdo) {
    if ($pdo === null) {
        error_log("PDO object is null in initializeDatabase. Cannot initialize database.");
        return;
    }

    try {
        // Create users table
        $pdo->exec("CREATE TABLE IF NOT EXISTS users (
            id SERIAL PRIMARY KEY,
            username VARCHAR(255) UNIQUE NOT NULL,
            password_hash VARCHAR(255) NOT NULL,
            clan_id INTEGER DEFAULT NULL,
            merit BIGINT DEFAULT 0,
            level INTEGER DEFAULT 1,
            experience BIGINT DEFAULT 0,
            money BIGINT DEFAULT 0,
            level_up_threshold BIGINT DEFAULT 100,
            prestige_points INTEGER DEFAULT 0,
            last_passive_income_collection TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            total_clicks BIGINT DEFAULT 0,
            total_merit_earned BIGINT DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );");

        // Create clans table
        $pdo->exec("CREATE TABLE IF NOT EXISTS clans (
            id SERIAL PRIMARY KEY,
            clan_name VARCHAR(255) UNIQUE NOT NULL,
            total_merit BIGINT DEFAULT 0
        );");

        // Create user_sessions table (for persistent login, if needed later)
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_sessions (
            session_id VARCHAR(255) PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            expires_at TIMESTAMP NOT NULL
        );");

        // Create game_state table
        $pdo->exec("CREATE TABLE IF NOT EXISTS game_state (
            clan_id INTEGER PRIMARY KEY REFERENCES clans(id) ON DELETE CASCADE,
            total_marks BIGINT DEFAULT 0
        );");

        // Pre-populate clans table with Hong Kong's 18 districts if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM clans;");
        if ($stmt->fetchColumn() == 0) {
            $districts = [
                '中西區', '灣仔區', '東區', '南區',
                '油尖旺區', '深水埗區', '九龍城區', '黃大仙區', '觀塘區',
                '葵青區', '荃灣區', '屯門區', '元朗區', '北區', '大埔區', '沙田區', '西貢區',
                '離島區'
            ];
            $insertClanStmt = $pdo->prepare("INSERT INTO clans (clan_name) VALUES (?) RETURNING id;");
            $insertGameStateStmt = $pdo->prepare("INSERT INTO game_state (clan_id, total_marks) VALUES (?, 0);");
            foreach ($districts as $district) {
                $insertClanStmt->execute([$district]);
                $clanId = $insertClanStmt->fetchColumn();
                $insertGameStateStmt->execute([$clanId]);
            }
        }

        // Create skills table (Upgrades)
        $pdo->exec("CREATE TABLE IF NOT EXISTS skills (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            cost BIGINT NOT NULL,
            effect_type VARCHAR(50) NOT NULL, -- e.g., 'click_multiplier', 'passive_income_multiplier'
            effect_value FLOAT NOT NULL,
            required_building_id INTEGER DEFAULT NULL, -- Link upgrades to buildings
            required_building_level INTEGER DEFAULT NULL
        );");

        // Pre-populate skills if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM skills;");
        if ($stmt->fetchColumn() == 0) {
            $skills = [
                ['虔誠信徒', '點擊功德獲取量 x2', 1000, 'click_multiplier', 2, null, null],
                ['木魚加持', '所有建築的功德產出量 +10%', 5000, 'passive_income_multiplier', 1.1, null, null],
                ['金剛經', '點擊功德獲取量 x3', 10000, 'click_multiplier', 3, null, null],
                ['舍利子', '所有建築的功德產出量 +20%', 50000, 'passive_income_multiplier', 1.2, null, null]
            ];
            $insertSkillStmt = $pdo->prepare("INSERT INTO skills (name, description, cost, effect_type, effect_value, required_building_id, required_building_level) VALUES (?, ?, ?, ?, ?, ?, ?);");
            foreach ($skills as $skill) {
                $insertSkillStmt->execute($skill);
            }
        }

        // Create shop_items table (Boosts)
        $pdo->exec("CREATE TABLE IF NOT EXISTS shop_items (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            cost INTEGER NOT NULL,
            duration_seconds INTEGER,
            effect_type VARCHAR(50) NOT NULL, -- e.g., 'temp_click_boost', 'temp_passive_boost'
            effect_value FLOAT NOT NULL
        );");

        // Pre-populate shop_items if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM shop_items;");
        if ($stmt->fetchColumn() == 0) {
            $items = [
                ['功德加倍符', '60秒內點擊功德獲取量 x7', 50, 60, 'temp_click_boost', 7],
                ['財源滾滾', '300秒內所有建築功德產出量 x5', 100, 300, 'temp_passive_boost', 5]
            ];
            $insertItemStmt = $pdo->prepare("INSERT INTO shop_items (name, description, cost, duration_seconds, effect_type, effect_value) VALUES (?, ?, ?, ?, ?, ?);");
            foreach ($items as $item) {
                $insertItemStmt->execute($item);
            }
        }

        // Create missions table
        $pdo->exec("CREATE TABLE IF NOT EXISTS missions (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            description TEXT,
            type VARCHAR(50) NOT NULL, -- e.g., 'clicks', 'merit_earned', 'level_up'
            target BIGINT NOT NULL,
            reward_money BIGINT DEFAULT 0,
            reward_experience BIGINT DEFAULT 0
        );");

        // Pre-populate missions if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM missions;");
        if ($stmt->fetchColumn() == 0) {
            $missions = [
                ['初級念佛', '點擊木魚 1,000 次', 'clicks', 1000, 500, 200],
                ['功德無量', '賺取 10,000 功德', 'merit_earned', 10000, 1000, 500],
                ['初窺門徑', '達到等級 10', 'level_up', 10, 2000, 1000]
            ];
            $insertMissionStmt = $pdo->prepare("INSERT INTO missions (name, description, type, target, reward_money, reward_experience) VALUES (?, ?, ?, ?, ?, ?);");
            foreach ($missions as $mission) {
                $insertMissionStmt->execute($mission);
            }
        }

        // Create buildings table
        $pdo->exec("CREATE TABLE IF NOT EXISTS buildings (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            base_cost BIGINT NOT NULL,
            base_production FLOAT NOT NULL,
            cost_increase_ratio FLOAT NOT NULL
        );");

        // Pre-populate buildings if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM buildings;");
        if ($stmt->fetchColumn() == 0) {
            $buildings = [
                ['蒲團', '一個簡單的蒲團，用於打坐。', 15, 0.1, 1.15],
                ['佛經', '一本佛經，可以增加你的智慧。', 100, 1, 1.15],
                ['佛堂', '一個小佛堂，可以容納更多信徒。', 1100, 8, 1.15],
                ['寺廟', '一座宏偉的寺廟，可以吸引更多信徒。', 12000, 47, 1.15],
                ['佛塔', '一座高聳的佛塔，可以傳播你的信仰。', 130000, 260, 1.15]
            ];
            $insertBuildingStmt = $pdo->prepare("INSERT INTO buildings (name, description, base_cost, base_production, cost_increase_ratio) VALUES (?, ?, ?, ?, ?);");
            foreach ($buildings as $building) {
                $insertBuildingStmt->execute($building);
            }
        }

        // Create user_buildings table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_buildings (
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            building_id INTEGER NOT NULL REFERENCES buildings(id) ON DELETE CASCADE,
            level INTEGER DEFAULT 0,
            PRIMARY KEY (user_id, building_id)
        );");

        // Create achievements table
        $pdo->exec("CREATE TABLE IF NOT EXISTS achievements (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) UNIQUE NOT NULL,
            description TEXT,
            type VARCHAR(50) NOT NULL, -- e.g., 'total_clicks', 'total_merit_earned', 'building_level'
            target BIGINT NOT NULL,
            building_id_requirement INTEGER DEFAULT NULL -- For building-specific achievements
        );");

        // Pre-populate achievements if empty
        $stmt = $pdo->query("SELECT COUNT(*) FROM achievements;");
        if ($stmt->fetchColumn() == 0) {
            $achievements = [
                ['手速達人', '總點擊次數達到 10,000', 'total_clicks', 10000, null],
                ['功德圓滿', '總功德獲取量達到 1,000,000', 'total_merit_earned', 1000000, null],
                ['建築大師', '擁有一個 10 級的寺廟', 'building_level', 10, 4]
            ];
            $insertAchievementStmt = $pdo->prepare("INSERT INTO achievements (name, description, type, target, building_id_requirement) VALUES (?, ?, ?, ?, ?);");
            foreach ($achievements as $achievement) {
                $insertAchievementStmt->execute($achievement);
            }
        }

        // Create user_achievements table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_achievements (
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            achievement_id INTEGER NOT NULL REFERENCES achievements(id) ON DELETE CASCADE,
            unlocked_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (user_id, achievement_id)
        );");

        // Create user_skills table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_skills (
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            skill_id INTEGER NOT NULL REFERENCES skills(id) ON DELETE CASCADE,
            PRIMARY KEY (user_id, skill_id)
        );");

        // Create user_items table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_items (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            item_id INTEGER NOT NULL REFERENCES shop_items(id) ON DELETE CASCADE,
            expires_at TIMESTAMP NULL
        );");

        // Create user_missions table
        $pdo->exec("CREATE TABLE IF NOT EXISTS user_missions (
            id SERIAL PRIMARY KEY,
            user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
            mission_id INTEGER NOT NULL REFERENCES missions(id) ON DELETE CASCADE,
            progress INTEGER DEFAULT 0,
            completed BOOLEAN DEFAULT FALSE,
            assigned_at DATE DEFAULT CURRENT_DATE,
            UNIQUE (user_id, mission_id, assigned_at) -- Ensure a user gets each mission type once per day
        );");

        // echo "Database initialized successfully!"; // For debugging
    } catch (PDOException $e) {
        error_log("Database initialization failed: " . $e->getMessage());
        // echo "Database initialization failed: " . $e->getMessage(); // For debugging
    }
}

// Ensure initializeDatabase is called only if $pdo is successfully connected
if ($pdo) {
    initializeDatabase($pdo);
}

?>
