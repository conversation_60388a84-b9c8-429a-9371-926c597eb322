<?php
// api/give_test_items.php - Give user some test items for testing

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $pdo->beginTransaction();

    // Get some random items to give
    $itemsStmt = $pdo->query("SELECT * FROM items ORDER BY RANDOM() LIMIT 5");
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

    $itemsGiven = [];

    foreach ($items as $item) {
        $quantity = rand(1, min(5, $item['max_stack']));
        
        // Check if user already has this item
        $existingStmt = $pdo->prepare("SELECT * FROM user_inventory WHERE user_id = :user_id AND item_id = :item_id");
        $existingStmt->execute([':user_id' => $user_id, ':item_id' => $item['id']]);
        $existing = $existingStmt->fetch(PDO::FETCH_ASSOC);

        if ($existing) {
            // Update existing item quantity (check max stack)
            $new_quantity = min($existing['quantity'] + $quantity, $item['max_stack']);
            $actual_added = $new_quantity - $existing['quantity'];
            
            if ($actual_added > 0) {
                $updateStmt = $pdo->prepare("UPDATE user_inventory SET quantity = :quantity WHERE id = :id");
                $updateStmt->execute([':quantity' => $new_quantity, ':id' => $existing['id']]);
                
                $itemsGiven[] = [
                    'name' => $item['name'],
                    'quantity' => $actual_added,
                    'total' => $new_quantity
                ];
            }
        } else {
            // Add new item to inventory
            $insertStmt = $pdo->prepare("
                INSERT INTO user_inventory (user_id, item_id, quantity, obtained_at) 
                VALUES (:user_id, :item_id, :quantity, NOW())
            ");
            $insertStmt->execute([
                ':user_id' => $user_id,
                ':item_id' => $item['id'],
                ':quantity' => $quantity
            ]);
            
            $itemsGiven[] = [
                'name' => $item['name'],
                'quantity' => $quantity,
                'total' => $quantity
            ];
        }
    }

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => '獲得測試物品！',
        'items_given' => $itemsGiven
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Give test items error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
