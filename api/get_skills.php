<?php
// public/api/get_skills.php

session_start();

require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Get all skills
$stmt_all_skills = $pdo->query("SELECT * FROM skills ORDER BY cost ASC;");
$all_skills = $stmt_all_skills->fetchAll(PDO::FETCH_ASSOC);

// Get user's skills
$stmt_user_skills = $pdo->prepare("SELECT skill_id FROM user_skills WHERE user_id = :user_id;");
$stmt_user_skills->execute([':user_id' => $user_id]);
$user_skills = $stmt_user_skills->fetchAll(PDO::FETCH_COLUMN, 0);

echo json_encode([
    'success' => true,
    'all_skills' => $all_skills,
    'user_skills' => $user_skills
]);
?>