<?php
// api/perform_gacha.php - Perform gacha pull

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);

$user_id = $_SESSION['user_id'];
$pool_id = isset($data['pool_id']) ? (int)$data['pool_id'] : 0;
$pull_count = isset($data['pull_count']) ? (int)$data['pull_count'] : 1;

if ($pool_id <= 0 || $pull_count <= 0 || $pull_count > 10) {
    echo json_encode(['success' => false, 'message' => 'Invalid pool ID or pull count']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Get pool details
    $poolStmt = $pdo->prepare("SELECT * FROM gacha_pools WHERE id = :pool_id AND is_active = true");
    $poolStmt->execute([':pool_id' => $pool_id]);
    $pool = $poolStmt->fetch(PDO::FETCH_ASSOC);

    if (!$pool) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Pool not found or inactive']);
        exit;
    }

    // Check user's money
    $userStmt = $pdo->prepare("SELECT money FROM users WHERE id = :user_id");
    $userStmt->execute([':user_id' => $user_id]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    $total_cost = $pool['cost_amount'] * $pull_count;
    if ($user['money'] < $total_cost) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '功德不足']);
        exit;
    }

    // Get pool items with drop rates
    $itemsStmt = $pdo->prepare("
        SELECT gpi.*, i.name, i.description, i.type, i.rarity, i.icon, i.max_stack
        FROM gacha_pool_items gpi
        JOIN items i ON gpi.item_id = i.id
        WHERE gpi.pool_id = :pool_id
        ORDER BY gpi.drop_rate DESC
    ");
    $itemsStmt->execute([':pool_id' => $pool_id]);
    $poolItems = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

    if (empty($poolItems)) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Pool has no items']);
        exit;
    }

    // Perform pulls
    $results = [];
    for ($i = 0; $i < $pull_count; $i++) {
        $pulledItem = performSinglePull($poolItems);
        if ($pulledItem) {
            $results[] = $pulledItem;
        }
    }

    if (empty($results)) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Pull failed']);
        exit;
    }

    // Deduct cost from user
    $updateMoneyStmt = $pdo->prepare("UPDATE users SET money = money - :cost WHERE id = :user_id");
    $updateMoneyStmt->execute([':cost' => $total_cost, ':user_id' => $user_id]);

    // Add items to inventory and record history
    foreach ($results as $result) {
        // Add to inventory
        addItemToInventory($pdo, $user_id, $result['item_id'], $result['quantity']);
        
        // Record in gacha history
        $historyStmt = $pdo->prepare("
            INSERT INTO user_gacha_history (user_id, pool_id, item_id, quantity, pulled_at)
            VALUES (:user_id, :pool_id, :item_id, :quantity, NOW())
        ");
        $historyStmt->execute([
            ':user_id' => $user_id,
            ':pool_id' => $pool_id,
            ':item_id' => $result['item_id'],
            ':quantity' => $result['quantity']
        ]);
    }

    // Get updated user money
    $userStmt->execute([':user_id' => $user_id]);
    $updatedUser = $userStmt->fetch(PDO::FETCH_ASSOC);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'results' => $results,
        'total_cost' => $total_cost,
        'new_money' => $updatedUser['money'],
        'pool_name' => $pool['name']
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Perform gacha error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}

function performSinglePull($poolItems) {
    $random = mt_rand() / mt_getrandmax(); // 0.0 to 1.0
    $cumulativeRate = 0.0;
    
    foreach ($poolItems as $item) {
        $cumulativeRate += $item['drop_rate'];
        if ($random <= $cumulativeRate) {
            $quantity = rand($item['quantity_min'], $item['quantity_max']);
            return [
                'item_id' => $item['item_id'],
                'name' => $item['name'],
                'description' => $item['description'],
                'type' => $item['type'],
                'rarity' => $item['rarity'],
                'icon' => $item['icon'],
                'quantity' => $quantity,
                'is_featured' => $item['is_featured']
            ];
        }
    }
    
    // Fallback to first item if something goes wrong
    $item = $poolItems[0];
    $quantity = rand($item['quantity_min'], $item['quantity_max']);
    return [
        'item_id' => $item['item_id'],
        'name' => $item['name'],
        'description' => $item['description'],
        'type' => $item['type'],
        'rarity' => $item['rarity'],
        'icon' => $item['icon'],
        'quantity' => $quantity,
        'is_featured' => $item['is_featured']
    ];
}

function addItemToInventory($pdo, $user_id, $item_id, $quantity) {
    // Check if user already has this item
    $existingStmt = $pdo->prepare("SELECT * FROM user_inventory WHERE user_id = :user_id AND item_id = :item_id");
    $existingStmt->execute([':user_id' => $user_id, ':item_id' => $item_id]);
    $existing = $existingStmt->fetch(PDO::FETCH_ASSOC);

    if ($existing) {
        // Update existing item quantity
        $updateStmt = $pdo->prepare("UPDATE user_inventory SET quantity = quantity + :quantity WHERE id = :id");
        $updateStmt->execute([':quantity' => $quantity, ':id' => $existing['id']]);
    } else {
        // Add new item to inventory
        $insertStmt = $pdo->prepare("
            INSERT INTO user_inventory (user_id, item_id, quantity, obtained_at) 
            VALUES (:user_id, :item_id, :quantity, NOW())
        ");
        $insertStmt->execute([
            ':user_id' => $user_id,
            ':item_id' => $item_id,
            ':quantity' => $quantity
        ]);
    }
}
?>
