<?php
// api/get_gacha_pools.php - Get available gacha pools and their items

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get all active gacha pools
    $poolsStmt = $pdo->query("
        SELECT * FROM gacha_pools 
        WHERE is_active = true 
        ORDER BY cost_amount ASC
    ");
    $pools = $poolsStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get pool items with item details for each pool
    $poolsWithItems = [];
    foreach ($pools as $pool) {
        $itemsStmt = $pdo->prepare("
            SELECT 
                gpi.*,
                i.name,
                i.description,
                i.type,
                i.rarity,
                i.icon,
                i.sell_price
            FROM gacha_pool_items gpi
            JOIN items i ON gpi.item_id = i.id
            WHERE gpi.pool_id = :pool_id
            ORDER BY gpi.drop_rate DESC, i.rarity DESC
        ");
        $itemsStmt->execute([':pool_id' => $pool['id']]);
        $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Calculate total drop rate for validation
        $totalRate = array_sum(array_column($items, 'drop_rate'));
        
        $pool['items'] = $items;
        $pool['total_drop_rate'] = $totalRate;
        $pool['featured_items'] = array_filter($items, function($item) {
            return $item['is_featured'];
        });
        
        $poolsWithItems[] = $pool;
    }

    // Get user's current money for cost checking
    $userStmt = $pdo->prepare("SELECT money FROM users WHERE id = :user_id");
    $userStmt->execute([':user_id' => $user_id]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    // Get user's recent gacha history (last 10 pulls)
    $historyStmt = $pdo->prepare("
        SELECT 
            ugh.*,
            i.name as item_name,
            i.rarity,
            i.icon,
            gp.name as pool_name
        FROM user_gacha_history ugh
        JOIN items i ON ugh.item_id = i.id
        JOIN gacha_pools gp ON ugh.pool_id = gp.id
        WHERE ugh.user_id = :user_id
        ORDER BY ugh.pulled_at DESC
        LIMIT 10
    ");
    $historyStmt->execute([':user_id' => $user_id]);
    $recentHistory = $historyStmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'pools' => $poolsWithItems,
        'user_money' => $user['money'],
        'recent_history' => $recentHistory
    ]);

} catch (PDOException $e) {
    error_log("Get gacha pools error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
