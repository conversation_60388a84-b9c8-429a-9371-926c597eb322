<?php
// public/api/golden_mokugyo.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Calculate the reward. It could be a multiplier of passive income, or a flat amount.
    // Let's make it 15 minutes of passive income.
    
    // 1. Calculate user's total passive income per second
    $stmt_passive_rate = $pdo->prepare("
        SELECT COALESCE(SUM(b.base_production * ub.level), 0) as total_passive_rate
        FROM user_buildings ub
        JOIN buildings b ON ub.building_id = b.id
        WHERE ub.user_id = :user_id;
    ");
    $stmt_passive_rate->execute([':user_id' => $user_id]);
    $passive_rate_data = $stmt_passive_rate->fetch(PDO::FETCH_ASSOC);
    $passive_rate_per_second = (float)$passive_rate_data['total_passive_rate'];

    // 2. Calculate the reward (900 seconds = 15 minutes)
    $reward_amount = floor($passive_rate_per_second * 900);

    // If user has no passive income, give a flat reward
    if ($reward_amount <= 0) {
        $reward_amount = 500;
    }

    // 3. Add the reward to the user's merit (money)
    $stmt_update_user = $pdo->prepare("
        UPDATE users 
        SET money = money + :reward_amount 
        WHERE id = :user_id
        RETURNING money;
    ");
    $stmt_update_user->execute([':reward_amount' => $reward_amount, ':user_id' => $user_id]);
    $new_money = $stmt_update_user->fetchColumn();

    echo json_encode([
        'success' => true, 
        'message' => "你點擊了金木魚，獲得了 {$reward_amount} 功德！",
        'reward' => $reward_amount,
        'new_money' => $new_money
    ]);

} catch (PDOException $e) {
    error_log("Golden Mokugyo error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred with the Golden Mokugyo.']);
}
?>