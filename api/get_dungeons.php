<?php
// api/get_dungeons.php - Get dungeons and user's progress

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get user level and energy
    $userStmt = $pdo->prepare("
        SELECT u.level, ue.current_energy, ue.max_energy
        FROM users u
        LEFT JOIN user_energy ue ON u.id = ue.user_id
        WHERE u.id = :user_id
    ");
    $userStmt->execute([':user_id' => $user_id]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    // Get all dungeons
    $dungeonsStmt = $pdo->query("
        SELECT * FROM dungeons 
        ORDER BY required_level ASC, id ASC
    ");
    $dungeons = $dungeonsStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get user's dungeon progress
    $progressStmt = $pdo->prepare("
        SELECT * FROM user_dungeon_progress 
        WHERE user_id = :user_id
    ");
    $progressStmt->execute([':user_id' => $user_id]);
    $userProgress = $progressStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // Create progress lookup
    $progressLookup = [];
    foreach ($userProgress as $progress) {
        $progressLookup[$progress['dungeon_id']] = $progress;
    }

    // Get floors and rewards for each dungeon
    $dungeonsWithDetails = [];
    foreach ($dungeons as $dungeon) {
        // Get floors
        $floorsStmt = $pdo->prepare("
            SELECT * FROM dungeon_floors 
            WHERE dungeon_id = :dungeon_id 
            ORDER BY floor_number ASC
        ");
        $floorsStmt->execute([':dungeon_id' => $dungeon['id']]);
        $floors = $floorsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Get rewards for each floor
        $floorsWithRewards = [];
        foreach ($floors as $floor) {
            $rewardsStmt = $pdo->prepare("
                SELECT dfr.*, i.name as item_name, i.icon as item_icon, i.rarity as item_rarity
                FROM dungeon_floor_rewards dfr
                LEFT JOIN items i ON dfr.item_id = i.id
                WHERE dfr.floor_id = :floor_id
                ORDER BY dfr.drop_rate DESC
            ");
            $rewardsStmt->execute([':floor_id' => $floor['id']]);
            $rewards = $rewardsStmt->fetchAll(PDO::FETCH_ASSOC);
            
            $floor['rewards'] = $rewards;
            $floorsWithRewards[] = $floor;
        }

        $userDungeonProgress = $progressLookup[$dungeon['id']] ?? null;
        $highestFloor = $userDungeonProgress ? $userDungeonProgress['highest_floor_cleared'] : 0;
        
        $dungeon['floors'] = $floorsWithRewards;
        $dungeon['user_progress'] = $userDungeonProgress;
        $dungeon['highest_floor_cleared'] = $highestFloor;
        $dungeon['is_unlocked'] = $user['level'] >= $dungeon['required_level'];
        $dungeon['can_enter'] = $dungeon['is_unlocked'] && ($user['current_energy'] >= $dungeon['energy_cost_per_floor']);
        $dungeon['next_floor'] = min($highestFloor + 1, $dungeon['max_floors']);
        
        $dungeonsWithDetails[] = $dungeon;
    }

    echo json_encode([
        'success' => true,
        'dungeons' => $dungeonsWithDetails,
        'user_energy' => [
            'current' => $user['current_energy'] ?? 100,
            'max' => $user['max_energy'] ?? 100
        ],
        'user_level' => $user['level']
    ]);

} catch (PDOException $e) {
    error_log("Get dungeons error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
