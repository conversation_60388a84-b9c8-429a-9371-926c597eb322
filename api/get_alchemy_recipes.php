<?php
// api/get_alchemy_recipes.php - Get alchemy recipes and user's crafting progress

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get user level for recipe requirements
    $userStmt = $pdo->prepare("SELECT level FROM users WHERE id = :user_id");
    $userStmt->execute([':user_id' => $user_id]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    // Get all alchemy recipes with materials
    $recipesStmt = $pdo->query("
        SELECT ar.*, ri.name as result_item_name, ri.description as result_description, 
               ri.rarity as result_rarity, ri.icon as result_icon
        FROM alchemy_recipes ar
        JOIN items ri ON ar.result_item_id = ri.id
        ORDER BY ar.required_level ASC, ar.id ASC
    ");
    $recipes = $recipesStmt->fetchAll(PDO::FETCH_ASSOC);

    // Get materials for each recipe
    $recipesWithMaterials = [];
    foreach ($recipes as $recipe) {
        $materialsStmt = $pdo->prepare("
            SELECT rm.*, i.name as material_name, i.icon as material_icon, i.rarity as material_rarity
            FROM recipe_materials rm
            JOIN items i ON rm.material_item_id = i.id
            WHERE rm.recipe_id = :recipe_id
        ");
        $materialsStmt->execute([':recipe_id' => $recipe['id']]);
        $materials = $materialsStmt->fetchAll(PDO::FETCH_ASSOC);

        // Check if user has enough materials
        $canCraft = true;
        $userMaterials = [];
        
        foreach ($materials as &$material) {
            $userMatStmt = $pdo->prepare("
                SELECT quantity FROM user_inventory 
                WHERE user_id = :user_id AND item_id = :item_id
            ");
            $userMatStmt->execute([':user_id' => $user_id, ':item_id' => $material['material_item_id']]);
            $userMat = $userMatStmt->fetch(PDO::FETCH_ASSOC);
            
            $userQuantity = $userMat ? $userMat['quantity'] : 0;
            $material['user_quantity'] = $userQuantity;
            $material['has_enough'] = $userQuantity >= $material['quantity_required'];
            
            if (!$material['has_enough']) {
                $canCraft = false;
            }
        }

        $recipe['materials'] = $materials;
        $recipe['can_craft'] = $canCraft && ($user['level'] >= $recipe['required_level']);
        $recipe['level_requirement_met'] = $user['level'] >= $recipe['required_level'];
        
        $recipesWithMaterials[] = $recipe;
    }

    // Get user's current alchemy progress
    $progressStmt = $pdo->prepare("
        SELECT uap.*, ar.name as recipe_name, ar.craft_time_seconds,
               ar.result_item_id, ri.name as result_item_name, ri.icon as result_icon
        FROM user_alchemy_progress uap
        JOIN alchemy_recipes ar ON uap.recipe_id = ar.id
        JOIN items ri ON ar.result_item_id = ri.id
        WHERE uap.user_id = :user_id AND uap.is_completed = false
        ORDER BY uap.started_at DESC
    ");
    $progressStmt->execute([':user_id' => $user_id]);
    $currentProgress = $progressStmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate remaining time for each progress
    foreach ($currentProgress as &$progress) {
        $completionTime = new DateTime($progress['completion_time']);
        $now = new DateTime();
        $remainingSeconds = max(0, $completionTime->getTimestamp() - $now->getTimestamp());
        $progress['remaining_seconds'] = $remainingSeconds;
        $progress['is_ready'] = $remainingSeconds <= 0;
    }

    echo json_encode([
        'success' => true,
        'recipes' => $recipesWithMaterials,
        'current_progress' => $currentProgress,
        'user_level' => $user['level']
    ]);

} catch (PDOException $e) {
    error_log("Get alchemy recipes error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
