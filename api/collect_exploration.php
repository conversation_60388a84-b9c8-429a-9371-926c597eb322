<?php
// api/collect_exploration.php - Collect exploration rewards

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);

$user_id = $_SESSION['user_id'];
$progress_id = isset($data['progress_id']) ? (int)$data['progress_id'] : 0;

if ($progress_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid progress ID']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Get exploration progress with area details
    $progressStmt = $pdo->prepare("
        SELECT uep.*, ea.name as area_name
        FROM user_exploration_progress uep
        JOIN exploration_areas ea ON uep.area_id = ea.id
        WHERE uep.id = :progress_id AND uep.user_id = :user_id AND uep.is_completed = false
    ");
    $progressStmt->execute([':progress_id' => $progress_id, ':user_id' => $user_id]);
    $progress = $progressStmt->fetch(PDO::FETCH_ASSOC);

    if (!$progress) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Progress not found or already completed']);
        exit;
    }

    // Check if exploration is ready
    $completionTime = new DateTime($progress['completion_time']);
    $now = new DateTime();
    
    if ($now < $completionTime) {
        $pdo->rollBack();
        $remainingSeconds = $completionTime->getTimestamp() - $now->getTimestamp();
        echo json_encode([
            'success' => false, 
            'message' => '探索尚未完成',
            'remaining_seconds' => $remainingSeconds
        ]);
        exit;
    }

    // Get area rewards
    $rewardsStmt = $pdo->prepare("
        SELECT er.*, i.name as item_name, i.icon as item_icon, i.rarity as item_rarity
        FROM exploration_rewards er
        LEFT JOIN items i ON er.item_id = i.id
        WHERE er.area_id = :area_id
    ");
    $rewardsStmt->execute([':area_id' => $progress['area_id']]);
    $possibleRewards = $rewardsStmt->fetchAll(PDO::FETCH_ASSOC);

    // Calculate rewards based on drop rates
    $actualRewards = [];
    $totalMoney = 0;
    $totalExperience = 0;
    $itemsReceived = [];

    foreach ($possibleRewards as $reward) {
        $random = mt_rand() / mt_getrandmax();
        if ($random <= $reward['drop_rate']) {
            $quantity = rand($reward['quantity_min'], $reward['quantity_max']);
            
            if ($reward['reward_type'] === 'money') {
                $totalMoney += $reward['money_amount'] * $quantity;
            } elseif ($reward['reward_type'] === 'experience') {
                $totalExperience += $reward['experience_amount'] * $quantity;
            } elseif ($reward['reward_type'] === 'item' && $reward['item_id']) {
                addItemToInventory($pdo, $user_id, $reward['item_id'], $quantity);
                $itemsReceived[] = [
                    'item_id' => $reward['item_id'],
                    'name' => $reward['item_name'],
                    'icon' => $reward['item_icon'],
                    'rarity' => $reward['item_rarity'],
                    'quantity' => $quantity
                ];
            }
        }
    }

    // Update user money and experience
    if ($totalMoney > 0 || $totalExperience > 0) {
        $updateUserStmt = $pdo->prepare("
            UPDATE users 
            SET money = money + :money, experience = experience + :experience
            WHERE id = :user_id
        ");
        $updateUserStmt->execute([
            ':money' => $totalMoney,
            ':experience' => $totalExperience,
            ':user_id' => $user_id
        ]);
    }

    // Mark progress as completed
    $completeStmt = $pdo->prepare("UPDATE user_exploration_progress SET is_completed = true WHERE id = :progress_id");
    $completeStmt->execute([':progress_id' => $progress_id]);

    $pdo->commit();

    // Build reward message
    $rewardMessages = [];
    if ($totalMoney > 0) $rewardMessages[] = "功德 +{$totalMoney}";
    if ($totalExperience > 0) $rewardMessages[] = "經驗 +{$totalExperience}";
    foreach ($itemsReceived as $item) {
        $rewardMessages[] = "{$item['name']} x{$item['quantity']}";
    }

    $message = empty($rewardMessages) ? "探索完成，但沒有獲得任何獎勵" : "探索完成！獲得：" . implode(", ", $rewardMessages);

    echo json_encode([
        'success' => true,
        'message' => $message,
        'area_name' => $progress['area_name'],
        'rewards' => [
            'money' => $totalMoney,
            'experience' => $totalExperience,
            'items' => $itemsReceived
        ]
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Collect exploration error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}

function addItemToInventory($pdo, $user_id, $item_id, $quantity) {
    // Check if user already has this item
    $existingStmt = $pdo->prepare("SELECT * FROM user_inventory WHERE user_id = :user_id AND item_id = :item_id");
    $existingStmt->execute([':user_id' => $user_id, ':item_id' => $item_id]);
    $existing = $existingStmt->fetch(PDO::FETCH_ASSOC);

    if ($existing) {
        // Update existing item quantity
        $updateStmt = $pdo->prepare("UPDATE user_inventory SET quantity = quantity + :quantity WHERE id = :id");
        $updateStmt->execute([':quantity' => $quantity, ':id' => $existing['id']]);
    } else {
        // Add new item to inventory
        $insertStmt = $pdo->prepare("
            INSERT INTO user_inventory (user_id, item_id, quantity, obtained_at) 
            VALUES (:user_id, :item_id, :quantity, NOW())
        ");
        $insertStmt->execute([
            ':user_id' => $user_id,
            ':item_id' => $item_id,
            ':quantity' => $quantity
        ]);
    }
}
?>
