<?php
// public/api/collect_passive_income.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $pdo->beginTransaction();

    // 1. Get user's last collection time, prestige points, and passive income multiplier
    $stmt_user_data = $pdo->prepare("
        SELECT
            u.last_passive_income_collection,
            u.prestige_points,
            COALESCE(SUM(CASE WHEN s.effect_type = 'passive_income_multiplier' THEN s.effect_value ELSE 0 END), 0) as passive_multiplier_sum
        FROM users u
        LEFT JOIN user_skills us ON u.id = us.user_id
        LEFT JOIN skills s ON us.skill_id = s.id
        WHERE u.id = :user_id
        GROUP BY u.id, u.last_passive_income_collection, u.prestige_points;
    ");
    $stmt_user_data->execute([':user_id' => $user_id]);
    $user_data = $stmt_user_data->fetch(PDO::FETCH_ASSOC);

    // 2. Calculate user's total passive income per second from buildings
    $stmt_passive_rate = $pdo->prepare("
        SELECT COALESCE(SUM(b.base_production * ub.level), 0) as total_passive_rate
        FROM user_buildings ub
        JOIN buildings b ON ub.building_id = b.id
        WHERE ub.user_id = :user_id;
    ");
    $stmt_passive_rate->execute([':user_id' => $user_id]);
    $passive_rate_data = $stmt_passive_rate->fetch(PDO::FETCH_ASSOC);
    $base_passive_rate_per_second = (float)$passive_rate_data['total_passive_rate'];

    // 3. Calculate time difference and income earned
    $last_collection = new DateTime($user_data['last_passive_income_collection']);
    $now = new DateTime();
    $time_diff_seconds = $now->getTimestamp() - $last_collection->getTimestamp();
    
    $prestige_bonus = 1 + ($user_data['prestige_points'] * 0.01);
    $passive_multiplier = $user_data['passive_multiplier_sum'] > 0 ? $user_data['passive_multiplier_sum'] : 1;

    $income_earned = floor($time_diff_seconds * $base_passive_rate_per_second * $passive_multiplier * $prestige_bonus);

    if ($income_earned > 0) {
        // 4. Update user's money, merit and total merit earned
        $stmt_update_user = $pdo->prepare("
            UPDATE users
            SET
                money = money + :income_earned,
                merit = money + :income_earned,
                total_merit_earned = total_merit_earned + :income_earned,
                last_passive_income_collection = NOW()
            WHERE id = :user_id
            RETURNING money, experience, level, level_up_threshold;
        ");
        $stmt_update_user->execute([':income_earned' => $income_earned, ':user_id' => $user_id]);
        $updated_user = $stmt_update_user->fetch(PDO::FETCH_ASSOC);

        // Also update the clan's total marks
        if (isset($_SESSION['clan_id'])) {
            $clan_id = $_SESSION['clan_id'];
            $pdo->prepare("UPDATE game_state SET total_marks = total_marks + ? WHERE clan_id = ?")->execute([$income_earned, $clan_id]);
        }
        
        $pdo->commit();

        echo json_encode([
            'success' => true,
            'income_earned' => $income_earned,
            'money' => $updated_user['money'],
            'experience' => $updated_user['experience'],
            'level' => $updated_user['level'],
            'level_up_threshold' => $updated_user['level_up_threshold']
        ]);
    } else {
        $pdo->commit(); // Still commit to update the collection time
        $stmt_update_time = $pdo->prepare("UPDATE users SET last_passive_income_collection = NOW() WHERE id = :user_id");
        $stmt_update_time->execute([':user_id' => $user_id]);
        echo json_encode(['success' => true, 'income_earned' => 0]);
    }

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Collect passive income error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while collecting income.']);
}
?>