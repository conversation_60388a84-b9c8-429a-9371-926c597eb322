<?php
// api/test_click.php - Simple test endpoint for mokugyo clicks

session_start();
header('Content-Type: application/json');

require_once 'db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Get input data
$data = json_decode(file_get_contents('php://input'), true);
$increment = isset($data['increment']) ? (int)$data['increment'] : 1;

if ($increment <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid increment value']);
    exit;
}

try {
    $pdo->beginTransaction();
    
    // Get current user data
    $stmt = $pdo->prepare("SELECT money, merit, total_clicks, total_merit_earned FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user) {
        echo json_encode(['success' => false, 'message' => 'User not found']);
        exit;
    }
    
    // Calculate new values (simple increment for testing)
    $new_money = $user['money'] + $increment;
    $new_merit = $user['merit'] + $increment;
    $new_total_clicks = $user['total_clicks'] + 1;
    $new_total_merit_earned = $user['total_merit_earned'] + $increment;
    
    // Update user data
    $stmt = $pdo->prepare("
        UPDATE users 
        SET money = ?, merit = ?, total_clicks = ?, total_merit_earned = ? 
        WHERE id = ?
    ");
    $result = $stmt->execute([$new_money, $new_merit, $new_total_clicks, $new_total_merit_earned, $user_id]);
    
    if ($result) {
        $pdo->commit();
        echo json_encode([
            'success' => true,
            'message' => 'Click processed successfully',
            'old_money' => $user['money'],
            'new_money' => $new_money,
            'increment' => $increment,
            'total_clicks' => $new_total_clicks
        ]);
    } else {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Failed to update user data']);
    }
    
} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Test click error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
