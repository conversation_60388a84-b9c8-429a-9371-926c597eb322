<?php
// public/api/buy_building.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$building_id = isset($data['building_id']) ? (int)$data['building_id'] : 0;

if ($building_id > 0) {
    $user_id = $_SESSION['user_id'];

    try {
        $pdo->beginTransaction();

        // Get building details
        $stmt_building = $pdo->prepare("SELECT * FROM buildings WHERE id = :building_id;");
        $stmt_building->execute([':building_id' => $building_id]);
        $building = $stmt_building->fetch(PDO::FETCH_ASSOC);

        // Get user's money and current building level
        $stmt_user = $pdo->prepare("
            SELECT u.money, ub.level 
            FROM users u
            LEFT JOIN user_buildings ub ON u.id = ub.user_id AND ub.building_id = :building_id
            WHERE u.id = :user_id;
        ");
        $stmt_user->execute([':building_id' => $building_id, ':user_id' => $user_id]);
        $user = $stmt_user->fetch(PDO::FETCH_ASSOC);

        $current_level = $user['level'] ? (int)$user['level'] : 0;
        $cost = floor($building['base_cost'] * pow($building['cost_increase_ratio'], $current_level));

        if ($building && $user['money'] >= $cost) {
            // Deduct cost
            $new_money = $user['money'] - $cost;
            $stmt_update_money = $pdo->prepare("UPDATE users SET money = :new_money WHERE id = :user_id;");
            $stmt_update_money->execute([':new_money' => $new_money, ':user_id' => $user_id]);

            // Upsert building level for user
            $stmt_upsert_building = $pdo->prepare("
                INSERT INTO user_buildings (user_id, building_id, level) 
                VALUES (:user_id, :building_id, 1)
                ON CONFLICT (user_id, building_id) 
                DO UPDATE SET level = user_buildings.level + 1;
            ");
            $stmt_upsert_building->execute([':user_id' => $user_id, ':building_id' => $building_id]);
            
            $pdo->commit();
            echo json_encode(['success' => true, 'message' => 'Building purchased successfully', 'new_money' => $new_money]);
        } else {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => 'Not enough money or building not found']);
        }
    } catch (PDOException $e) {
        $pdo->rollBack();
        error_log("Buy building error: " . $e->getMessage());
        echo json_encode(['success' => false, 'message' => 'An error occurred during purchase.']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid building ID']);
}
?>