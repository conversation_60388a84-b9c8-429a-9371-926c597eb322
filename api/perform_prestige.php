<?php
// public/api/perform_prestige.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

// --- Prestige Configuration ---
const PRESTIGE_LEVEL_REQUIREMENT = 50; // Required level to prestige

// 1. Get current user state
$stmt_user = $pdo->prepare("SELECT level, prestige_points FROM users WHERE id = :user_id;");
$stmt_user->execute([':user_id' => $user_id]);
$user = $stmt_user->fetch(PDO::FETCH_ASSOC);

if ($user['level'] >= PRESTIGE_LEVEL_REQUIREMENT) {
    // 2. Calculate prestige points to award (e.g., 1 point per 10 levels)
    $points_to_add = floor($user['level'] / 10);

    // 3. Perform the prestige reset
    // Reset level, experience, money, and threshold. Increment prestige level and add points.
    $stmt_prestige_reset = $pdo->prepare("
        UPDATE users
        SET 
            level = 1,
            experience = 0,
            money = 0,
            level_up_threshold = 100,
            prestige_points = prestige_points + :points_to_add,
            last_passive_income_collection = NOW()
        WHERE id = :user_id;
    ");
    $stmt_prestige_reset->execute([':points_to_add' => $points_to_add, ':user_id' => $user_id]);

    // 4. Clear owned skills (a core part of prestiging)
    $stmt_clear_skills = $pdo->prepare("DELETE FROM user_skills WHERE user_id = :user_id;");
    $stmt_clear_skills->execute([':user_id' => $user_id]);
    
    // 5. Clear active boosts (optional, but recommended for a full reset)
    $stmt_clear_items = $pdo->prepare("DELETE FROM user_items WHERE user_id = :user_id;");
    $stmt_clear_items->execute([':user_id' => $user_id]);

    // 6. Reset all buildings to level 0 (CRITICAL FIX)
    $stmt_clear_buildings = $pdo->prepare("DELETE FROM user_buildings WHERE user_id = :user_id;");
    $stmt_clear_buildings->execute([':user_id' => $user_id]);

    echo json_encode(['success' => true, 'message' => "輪迴成功！你獲得了 {$points_to_add} 點舍利子！"]);

} else {
    // User does not meet the requirements
    echo json_encode(['success' => false, 'message' => '未達到輪迴等級 (需要 ' . PRESTIGE_LEVEL_REQUIREMENT . ' 級)']);
}
?>