<?php
// api/collect_alchemy.php - Collect completed alchemy items

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);

$user_id = $_SESSION['user_id'];
$progress_id = isset($data['progress_id']) ? (int)$data['progress_id'] : 0;

if ($progress_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid progress ID']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Get alchemy progress with recipe details
    $progressStmt = $pdo->prepare("
        SELECT uap.*, ar.name as recipe_name, ar.result_item_id, ar.result_quantity, 
               ar.success_rate, ri.name as result_item_name, ri.icon as result_icon
        FROM user_alchemy_progress uap
        JOIN alchemy_recipes ar ON uap.recipe_id = ar.id
        JOIN items ri ON ar.result_item_id = ri.id
        WHERE uap.id = :progress_id AND uap.user_id = :user_id AND uap.is_completed = false
    ");
    $progressStmt->execute([':progress_id' => $progress_id, ':user_id' => $user_id]);
    $progress = $progressStmt->fetch(PDO::FETCH_ASSOC);

    if (!$progress) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Progress not found or already completed']);
        exit;
    }

    // Check if alchemy is ready
    $completionTime = new DateTime($progress['completion_time']);
    $now = new DateTime();
    
    if ($now < $completionTime) {
        $pdo->rollBack();
        $remainingSeconds = $completionTime->getTimestamp() - $now->getTimestamp();
        echo json_encode([
            'success' => false, 
            'message' => '煉丹尚未完成',
            'remaining_seconds' => $remainingSeconds
        ]);
        exit;
    }

    // Determine success based on success rate
    $random = mt_rand() / mt_getrandmax();
    $isSuccess = $random <= $progress['success_rate'];

    $resultMessage = '';
    $itemsReceived = [];

    if ($isSuccess) {
        // Add result item to inventory
        addItemToInventory($pdo, $user_id, $progress['result_item_id'], $progress['result_quantity']);
        $resultMessage = "煉製成功！獲得 {$progress['result_item_name']} x{$progress['result_quantity']}";
        $itemsReceived[] = [
            'item_id' => $progress['result_item_id'],
            'name' => $progress['result_item_name'],
            'icon' => $progress['result_icon'],
            'quantity' => $progress['result_quantity']
        ];
    } else {
        $resultMessage = "煉製失敗！";
    }

    // Mark progress as completed
    $completeStmt = $pdo->prepare("UPDATE user_alchemy_progress SET is_completed = true WHERE id = :progress_id");
    $completeStmt->execute([':progress_id' => $progress_id]);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'is_craft_success' => $isSuccess,
        'message' => $resultMessage,
        'recipe_name' => $progress['recipe_name'],
        'items_received' => $itemsReceived,
        'success_rate' => $progress['success_rate']
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Collect alchemy error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}

function addItemToInventory($pdo, $user_id, $item_id, $quantity) {
    // Check if user already has this item
    $existingStmt = $pdo->prepare("SELECT * FROM user_inventory WHERE user_id = :user_id AND item_id = :item_id");
    $existingStmt->execute([':user_id' => $user_id, ':item_id' => $item_id]);
    $existing = $existingStmt->fetch(PDO::FETCH_ASSOC);

    if ($existing) {
        // Update existing item quantity
        $updateStmt = $pdo->prepare("UPDATE user_inventory SET quantity = quantity + :quantity WHERE id = :id");
        $updateStmt->execute([':quantity' => $quantity, ':id' => $existing['id']]);
    } else {
        // Add new item to inventory
        $insertStmt = $pdo->prepare("
            INSERT INTO user_inventory (user_id, item_id, quantity, obtained_at) 
            VALUES (:user_id, :item_id, :quantity, NOW())
        ");
        $insertStmt->execute([
            ':user_id' => $user_id,
            ':item_id' => $item_id,
            ':quantity' => $quantity
        ]);
    }
}
?>
