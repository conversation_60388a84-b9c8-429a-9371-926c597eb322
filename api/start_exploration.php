<?php
// api/start_exploration.php - Start exploration in an area

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);

$user_id = $_SESSION['user_id'];
$area_id = isset($data['area_id']) ? (int)$data['area_id'] : 0;

if ($area_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid area ID']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Get area details
    $areaStmt = $pdo->prepare("SELECT * FROM exploration_areas WHERE id = :area_id");
    $areaStmt->execute([':area_id' => $area_id]);
    $area = $areaStmt->fetch(PDO::FETCH_ASSOC);

    if (!$area) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Area not found']);
        exit;
    }

    // Check user level requirement
    $userStmt = $pdo->prepare("
        SELECT u.level, ue.current_energy, ue.max_energy
        FROM users u
        LEFT JOIN user_energy ue ON u.id = ue.user_id
        WHERE u.id = :user_id
    ");
    $userStmt->execute([':user_id' => $user_id]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    if ($user['level'] < $area['required_level']) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '等級不足']);
        exit;
    }

    // Check energy
    if ($user['current_energy'] < $area['energy_cost']) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '體力不足']);
        exit;
    }

    // Check if user already has ongoing exploration
    $ongoingStmt = $pdo->prepare("
        SELECT COUNT(*) FROM user_exploration_progress 
        WHERE user_id = :user_id AND is_completed = false
    ");
    $ongoingStmt->execute([':user_id' => $user_id]);
    $ongoingCount = $ongoingStmt->fetchColumn();

    if ($ongoingCount > 0) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '已有正在進行的探索']);
        exit;
    }

    // Consume energy
    $updateEnergyStmt = $pdo->prepare("
        UPDATE user_energy 
        SET current_energy = current_energy - :energy_cost
        WHERE user_id = :user_id
    ");
    $updateEnergyStmt->execute([':energy_cost' => $area['energy_cost'], ':user_id' => $user_id]);

    // Start exploration
    $completionTime = new DateTime();
    $completionTime->add(new DateInterval('PT' . $area['exploration_time_seconds'] . 'S'));

    $startExplorationStmt = $pdo->prepare("
        INSERT INTO user_exploration_progress (user_id, area_id, started_at, completion_time, is_completed, energy_spent)
        VALUES (:user_id, :area_id, NOW(), :completion_time, false, :energy_spent)
    ");
    $startExplorationStmt->execute([
        ':user_id' => $user_id,
        ':area_id' => $area_id,
        ':completion_time' => $completionTime->format('Y-m-d H:i:s'),
        ':energy_spent' => $area['energy_cost']
    ]);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => "開始探索 {$area['name']}",
        'area_name' => $area['name'],
        'completion_time' => $completionTime->format('Y-m-d H:i:s'),
        'exploration_time_seconds' => $area['exploration_time_seconds'],
        'energy_spent' => $area['energy_cost']
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Start exploration error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
