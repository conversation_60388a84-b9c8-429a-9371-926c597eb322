<?php
// public/api/claim_mission_reward.php

session_start();

require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$user_mission_id = isset($data['user_mission_id']) ? (int)$data['user_mission_id'] : 0;

if ($user_mission_id > 0) {
    $user_id = $_SESSION['user_id'];

    // Get user mission details
    $stmt_user_mission = $pdo->prepare("
        SELECT 
            um.id,
            um.progress,
            um.completed,
            m.target,
            m.reward_money,
            m.reward_experience
        FROM user_missions um
        JOIN missions m ON um.mission_id = m.id
        WHERE um.id = :user_mission_id AND um.user_id = :user_id;
    ");
    $stmt_user_mission->execute([':user_mission_id' => $user_mission_id, ':user_id' => $user_id]);
    $user_mission = $stmt_user_mission->fetch(PDO::FETCH_ASSOC);

    if ($user_mission && $user_mission['progress'] >= $user_mission['target'] && !$user_mission['completed']) {
        // Mark mission as completed
        $stmt_complete_mission = $pdo->prepare("UPDATE user_missions SET completed = TRUE WHERE id = :user_mission_id;");
        $stmt_complete_mission->execute([':user_mission_id' => $user_mission_id]);

        // Fetch user data BEFORE reward to get current state
        $stmt_user = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
        $stmt_user->execute([':user_id' => $user_id]);
        $user = $stmt_user->fetch(PDO::FETCH_ASSOC);

        // Add rewards to user
        $new_money = $user['money'] + $user_mission['reward_money'];
        $new_experience = $user['experience'] + $user_mission['reward_experience'];
        
        // Check for level up
        $level_up = false;
        $current_level = $user['level'];
        $current_threshold = $user['level_up_threshold'];

        if ($new_experience >= $current_threshold) {
            $level_up = true;
            $current_level += 1;
            // Carry over the remainder of the experience
            $new_experience -= $current_threshold; 
            $current_threshold *= 1.5; // Increase threshold for the next level

            $stmt_level_up = $pdo->prepare("
                UPDATE users 
                SET level = :level, experience = :experience, money = :money, level_up_threshold = :threshold
                WHERE id = :user_id
            ");
            $stmt_level_up->execute([
                ':level' => $current_level,
                ':experience' => $new_experience,
                ':money' => $new_money,
                ':threshold' => $current_threshold,
                ':user_id' => $user_id
            ]);
        } else {
            // Just update money and experience if no level up
            $stmt_update = $pdo->prepare("UPDATE users SET money = :money, experience = :experience WHERE id = :user_id");
            $stmt_update->execute([
                ':money' => $new_money,
                ':experience' => $new_experience,
                ':user_id' => $user_id
            ]);
        }

        echo json_encode([
            'success' => true,
            'message' => 'Reward claimed successfully',
            'experience' => $new_experience,
            'money' => $new_money,
            'level' => $current_level,
            'level_up_threshold' => $current_threshold,
            'level_up' => $level_up,
            'new_level' => $level_up ? $current_level : null
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Mission not completed or already claimed']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid mission ID']);
}
?>