<?php
// api/assign_missions.php - Manual mission assignment endpoint

session_start();
header('Content-Type: application/json');

require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    $pdo->beginTransaction();
    
    // Get all missions
    $stmt_missions = $pdo->query("SELECT id, name FROM missions;");
    $missions = $stmt_missions->fetchAll(PDO::FETCH_ASSOC);
    
    $assigned_count = 0;
    $already_assigned = 0;
    
    foreach ($missions as $mission) {
        $mission_id = $mission['id'];
        
        // Check if user already has this mission for today
        $stmt_check = $pdo->prepare("
            SELECT id FROM user_missions
            WHERE user_id = :user_id AND mission_id = :mission_id AND assigned_at = CURRENT_DATE;
        ");
        $stmt_check->execute([':user_id' => $user_id, ':mission_id' => $mission_id]);

        if ($stmt_check->rowCount() == 0) {
            // Assign the mission
            $stmt_assign = $pdo->prepare("
                INSERT INTO user_missions (user_id, mission_id, assigned_at)
                VALUES (:user_id, :mission_id, CURRENT_DATE);
            ");
            $stmt_assign->execute([':user_id' => $user_id, ':mission_id' => $mission_id]);
            $assigned_count++;
        } else {
            $already_assigned++;
        }
    }
    
    $pdo->commit();
    
    echo json_encode([
        'success' => true,
        'message' => "任務分配完成",
        'assigned_count' => $assigned_count,
        'already_assigned' => $already_assigned,
        'total_missions' => count($missions)
    ]);
    
} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Mission assignment error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error: ' . $e->getMessage()]);
}
?>
