<?php
// public/api/get_achievements.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get all achievements
    $stmt_all_achievements = $pdo->query("SELECT * FROM achievements ORDER BY id ASC;");
    $all_achievements = $stmt_all_achievements->fetchAll(PDO::FETCH_ASSOC);

    // Get user's unlocked achievements
    $stmt_user_achievements = $pdo->prepare("SELECT achievement_id, unlocked_at FROM user_achievements WHERE user_id = :user_id;");
    $stmt_user_achievements->execute([':user_id' => $user_id]);
    $user_achievements_data = $stmt_user_achievements->fetchAll(PDO::FETCH_ASSOC);

    // Create a map of user's achievements for easy lookup
    $user_achievements = [];
    foreach ($user_achievements_data as $achievement) {
        $user_achievements[$achievement['achievement_id']] = $achievement['unlocked_at'];
    }

    echo json_encode([
        'success' => true,
        'all_achievements' => $all_achievements,
        'user_achievements' => $user_achievements
    ]);

} catch (PDOException $e) {
    error_log("Get achievements error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while fetching achievement data.']);
}
?>