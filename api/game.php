<?php
header('Content-Type: application/json');
require_once 'db_connect.php';

$response = ['success' => false, 'message' => 'An unknown error occurred.'];

// Ensure $pdo is initialized and not null
if ($pdo === null) {
    $response['message'] = 'Database connection not established.';
    echo json_encode($response);
    exit();
}

$input = json_decode(file_get_contents('php://input'), true);

if (isset($input['action'])) {
    switch ($input['action']) {
        case 'addMerit':
            if (isset($input['username']) && isset($input['merit'])) {
                $username = $input['username'];
                $meritToAdd = (int)$input['merit'];

                try {
                    $pdo->beginTransaction();

                    // Update user's merit
                    $stmt = $pdo->prepare("UPDATE users SET merit = merit + ? WHERE username = ? RETURNING clan_id;");
                    $stmt->execute([$meritToAdd, $username]);
                    $user = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($user && $user['clan_id']) {
                        // Update clan's total merit
                        $clanUpdateStmt = $pdo->prepare("UPDATE clans SET total_merit = total_merit + ? WHERE id = ?;");
                        $clanUpdateStmt->execute([$meritToAdd, $user['clan_id']]);
                    }

                    $pdo->commit();
                    $response['success'] = true;
                    $response['message'] = 'Merit updated successfully.';
                } catch (PDOException $e) {
                    $pdo->rollBack();
                    error_log("Add merit error: " . $e->getMessage());
                    $response['message'] = 'An error occurred while adding merit.';
                }
            } else {
                $response['message'] = 'Username and merit amount are required.';
            }
            break;

        case 'getClanScores':
            try {
                $stmt = $pdo->query("SELECT clan_name, total_merit FROM clans ORDER BY total_merit DESC;");
                $scores = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $response['success'] = true;
                $response['scores'] = $scores;
            } catch (PDOException $e) {
                error_log("Get clan scores error: " . $e->getMessage());
                $response['message'] = 'An error occurred while fetching clan scores.';
            }
            break;

        case 'updateLevel':
            if (isset($input['username']) && isset($input['level'])) {
                $username = $input['username'];
                $newLevel = (int)$input['level'];

                try {
                    $stmt = $pdo->prepare("UPDATE users SET level = ? WHERE username = ?");
                    if ($stmt->execute([$newLevel, $username])) {
                        $response['success'] = true;
                        $response['message'] = 'User level updated successfully.';
                    } else {
                        $response['message'] = 'Failed to update user level.';
                    }
                } catch (PDOException $e) {
                    error_log("Update level error: " . $e->getMessage());
                    $response['message'] = 'An error occurred while updating user level.';
                }
            } else {
                $response['message'] = 'Username and level are required.';
            }
            break;

        default:
            $response['message'] = 'Invalid action.';
            break;
    }
} else {
    $response['message'] = 'No action specified.';
}

echo json_encode($response);
?>