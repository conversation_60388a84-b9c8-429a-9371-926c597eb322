<?php
// api/sell_item.php - Sell item from user's inventory

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);

$user_id = $_SESSION['user_id'];
$inventory_id = isset($data['inventory_id']) ? (int)$data['inventory_id'] : 0;
$quantity = isset($data['quantity']) ? (int)$data['quantity'] : 1;

if ($inventory_id <= 0 || $quantity <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid inventory ID or quantity']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Get inventory item with item details
    $stmt = $pdo->prepare("
        SELECT ui.*, i.name, i.sell_price 
        FROM user_inventory ui
        JOIN items i ON ui.item_id = i.id
        WHERE ui.id = :inventory_id AND ui.user_id = :user_id
    ");
    $stmt->execute([':inventory_id' => $inventory_id, ':user_id' => $user_id]);
    $inventoryItem = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$inventoryItem) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Item not found in inventory']);
        exit;
    }

    if ($quantity > $inventoryItem['quantity']) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Not enough items to sell']);
        exit;
    }

    // Calculate sell value
    $sell_value = $inventoryItem['sell_price'] * $quantity;

    // Update user money
    $updateMoneyStmt = $pdo->prepare("UPDATE users SET money = money + :sell_value WHERE id = :user_id");
    $updateMoneyStmt->execute([':sell_value' => $sell_value, ':user_id' => $user_id]);

    // Update or remove inventory item
    $new_quantity = $inventoryItem['quantity'] - $quantity;
    if ($new_quantity <= 0) {
        // Remove item completely
        $deleteStmt = $pdo->prepare("DELETE FROM user_inventory WHERE id = :inventory_id");
        $deleteStmt->execute([':inventory_id' => $inventory_id]);
    } else {
        // Update quantity
        $updateStmt = $pdo->prepare("UPDATE user_inventory SET quantity = :quantity WHERE id = :inventory_id");
        $updateStmt->execute([':quantity' => $new_quantity, ':inventory_id' => $inventory_id]);
    }

    // Get updated user money
    $userStmt = $pdo->prepare("SELECT money FROM users WHERE id = :user_id");
    $userStmt->execute([':user_id' => $user_id]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => "出售 {$inventoryItem['name']} x{$quantity}，獲得 {$sell_value} 功德",
        'sell_value' => $sell_value,
        'new_money' => $user['money'],
        'quantity_sold' => $quantity
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Sell item error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
