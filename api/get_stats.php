<?php
// public/api/get_stats.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Get user stats
    $stmt_user_stats = $pdo->prepare("
        SELECT 
            total_clicks, 
            total_merit_earned, 
            level, 
            prestige_points,
            (SELECT COUNT(*) FROM user_achievements WHERE user_id = :user_id) as achievements_unlocked,
            (SELECT COUNT(*) FROM user_buildings WHERE user_id = :user_id AND level > 0) as buildings_owned
        FROM users 
        WHERE id = :user_id;
    ");
    $stmt_user_stats->execute([':user_id' => $user_id]);
    $stats = $stmt_user_stats->fetch(PDO::FETCH_ASSOC);

    echo json_encode(['success' => true, 'stats' => $stats]);

} catch (PDOException $e) {
    error_log("Get stats error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'An error occurred while fetching user stats.']);
}
?>