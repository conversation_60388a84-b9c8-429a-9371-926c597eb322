<?php
// public/api/get_shop_items.php

session_start();
require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

// Get all shop items
$stmt_all_items = $pdo->query("SELECT id, name, description, cost, duration_seconds FROM shop_items ORDER BY cost ASC;");
$all_items = $stmt_all_items->fetchAll(PDO::FETCH_ASSOC);

// Get user's active items (boosts)
$user_id = $_SESSION['user_id'];
$stmt_user_items = $pdo->prepare("
    SELECT item_id, expires_at 
    FROM user_items 
    WHERE user_id = :user_id AND expires_at > NOW();
");
$stmt_user_items->execute([':user_id' => $user_id]);
$user_items = $stmt_user_items->fetchAll(PDO::FETCH_ASSOC);

echo json_encode([
    'success' => true,
    'all_items' => $all_items,
    'user_items' => $user_items
]);
?>