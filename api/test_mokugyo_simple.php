<?php
// api/test_mokugyo_simple.php - Simplified mokugyo test that mimics the working database test

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Get input data (same as update_score.php)
$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);
$increment = isset($data['increment']) ? (int)$data['increment'] : 0;

error_log("Simple mokugyo test - Raw input: " . $raw_input);
error_log("Simple mokugyo test - Parsed data: " . json_encode($data));
error_log("Simple mokugyo test - Increment: " . $increment);

if ($increment > 0) {
    try {
        $pdo->beginTransaction();
        
        // Get current user data (simple query like the working database test)
        $stmt = $pdo->prepare("SELECT money, merit, experience, level FROM users WHERE id = ?");
        $stmt->execute([$user_id]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception("User not found");
        }
        
        error_log("Simple mokugyo test - Current user data: " . json_encode($user));
        
        // Calculate new values (simple increment like database test)
        $new_money = $user['money'] + $increment;
        $new_merit = $user['merit'] + $increment;
        
        error_log("Simple mokugyo test - New money: $new_money, New merit: $new_merit");
        
        // Update database (same as database test)
        $stmt_update = $pdo->prepare("UPDATE users SET money = ?, merit = ? WHERE id = ?");
        $update_result = $stmt_update->execute([$new_money, $new_merit, $user_id]);
        $rows_affected = $stmt_update->rowCount();
        
        error_log("Simple mokugyo test - Update result: " . ($update_result ? 'true' : 'false') . ", rows affected: $rows_affected");
        
        if ($update_result && $rows_affected > 0) {
            $pdo->commit();
            
            // Verify the update (same as database test)
            $stmt_verify = $pdo->prepare("SELECT money, merit FROM users WHERE id = ?");
            $stmt_verify->execute([$user_id]);
            $verified_data = $stmt_verify->fetch(PDO::FETCH_ASSOC);
            
            error_log("Simple mokugyo test - Verified data: " . json_encode($verified_data));
            
            echo json_encode([
                'success' => true,
                'message' => 'Simple mokugyo test successful',
                'money' => $verified_data['money'],
                'merit' => $verified_data['merit'],
                'increment' => $increment,
                'old_money' => $user['money'],
                'new_money' => $verified_data['money'],
                'update_result' => $update_result,
                'rows_affected' => $rows_affected
            ]);
        } else {
            $pdo->rollBack();
            echo json_encode([
                'success' => false,
                'message' => 'Update failed',
                'update_result' => $update_result,
                'rows_affected' => $rows_affected
            ]);
        }
        
    } catch (Exception $e) {
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        error_log("Simple mokugyo test error: " . $e->getMessage());
        echo json_encode([
            'success' => false,
            'message' => 'Exception: ' . $e->getMessage()
        ]);
    }
} else {
    error_log("Simple mokugyo test - Invalid increment: $increment");
    echo json_encode([
        'success' => false,
        'message' => 'Invalid increment',
        'increment' => $increment,
        'data' => $data
    ]);
}
?>
