<?php
// public/api/get_missions.php

session_start();

require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Get user's missions for today
$stmt_user_missions = $pdo->prepare("
    SELECT 
        um.id,
        m.name,
        m.description,
        m.target,
        um.progress,
        um.completed
    FROM user_missions um
    JOIN missions m ON um.mission_id = m.id
    WHERE um.user_id = :user_id AND um.assigned_at = CURRENT_DATE;
");
$stmt_user_missions->execute([':user_id' => $user_id]);
$user_missions = $stmt_user_missions->fetchAll(PDO::FETCH_ASSOC);

echo json_encode([
    'success' => true,
    'missions' => $user_missions
]);
?>