<?php
// public/api/get_missions.php

session_start();

require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

// Function to assign daily missions if not already assigned
function assignDailyMissions($pdo, $user_id) {
    try {
        // Get all missions
        $stmt_missions = $pdo->query("SELECT id FROM missions;");
        $missions = $stmt_missions->fetchAll(PDO::FETCH_COLUMN, 0);

        foreach ($missions as $mission_id) {
            // Check if user already has this mission for today
            $stmt_check = $pdo->prepare("
                SELECT id FROM user_missions
                WHERE user_id = :user_id AND mission_id = :mission_id AND assigned_at = CURRENT_DATE;
            ");
            $stmt_check->execute([':user_id' => $user_id, ':mission_id' => $mission_id]);

            if ($stmt_check->rowCount() == 0) {
                // Assign the mission
                $stmt_assign = $pdo->prepare("
                    INSERT INTO user_missions (user_id, mission_id, assigned_at)
                    VALUES (:user_id, :mission_id, CURRENT_DATE);
                ");
                $stmt_assign->execute([':user_id' => $user_id, ':mission_id' => $mission_id]);
            }
        }
    } catch (PDOException $e) {
        error_log("Error assigning daily missions: " . $e->getMessage());
    }
}

// Assign daily missions if not already assigned
assignDailyMissions($pdo, $user_id);

// Get user's missions for today
$stmt_user_missions = $pdo->prepare("
    SELECT 
        um.id,
        m.name,
        m.description,
        m.target,
        um.progress,
        um.completed
    FROM user_missions um
    JOIN missions m ON um.mission_id = m.id
    WHERE um.user_id = :user_id AND um.assigned_at = CURRENT_DATE;
");
$stmt_user_missions->execute([':user_id' => $user_id]);
$user_missions = $stmt_user_missions->fetchAll(PDO::FETCH_ASSOC);

echo json_encode([
    'success' => true,
    'missions' => $user_missions
]);
?>