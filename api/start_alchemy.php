<?php
// api/start_alchemy.php - Start alchemy crafting

session_start();
require_once 'db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$raw_input = file_get_contents('php://input');
$data = json_decode($raw_input, true);

$user_id = $_SESSION['user_id'];
$recipe_id = isset($data['recipe_id']) ? (int)$data['recipe_id'] : 0;

if ($recipe_id <= 0) {
    echo json_encode(['success' => false, 'message' => 'Invalid recipe ID']);
    exit;
}

try {
    $pdo->beginTransaction();

    // Get recipe details
    $recipeStmt = $pdo->prepare("
        SELECT ar.*, ri.name as result_item_name
        FROM alchemy_recipes ar
        JOIN items ri ON ar.result_item_id = ri.id
        WHERE ar.id = :recipe_id
    ");
    $recipeStmt->execute([':recipe_id' => $recipe_id]);
    $recipe = $recipeStmt->fetch(PDO::FETCH_ASSOC);

    if (!$recipe) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => 'Recipe not found']);
        exit;
    }

    // Check user level requirement
    $userStmt = $pdo->prepare("SELECT level FROM users WHERE id = :user_id");
    $userStmt->execute([':user_id' => $user_id]);
    $user = $userStmt->fetch(PDO::FETCH_ASSOC);

    if ($user['level'] < $recipe['required_level']) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '等級不足']);
        exit;
    }

    // Check if user already has ongoing alchemy
    $ongoingStmt = $pdo->prepare("
        SELECT COUNT(*) FROM user_alchemy_progress 
        WHERE user_id = :user_id AND is_completed = false
    ");
    $ongoingStmt->execute([':user_id' => $user_id]);
    $ongoingCount = $ongoingStmt->fetchColumn();

    if ($ongoingCount > 0) {
        $pdo->rollBack();
        echo json_encode(['success' => false, 'message' => '已有正在進行的煉丹']);
        exit;
    }

    // Get required materials
    $materialsStmt = $pdo->prepare("
        SELECT rm.*, i.name as material_name
        FROM recipe_materials rm
        JOIN items i ON rm.material_item_id = i.id
        WHERE rm.recipe_id = :recipe_id
    ");
    $materialsStmt->execute([':recipe_id' => $recipe_id]);
    $materials = $materialsStmt->fetchAll(PDO::FETCH_ASSOC);

    // Check and consume materials
    foreach ($materials as $material) {
        $userMatStmt = $pdo->prepare("
            SELECT * FROM user_inventory 
            WHERE user_id = :user_id AND item_id = :item_id
        ");
        $userMatStmt->execute([':user_id' => $user_id, ':item_id' => $material['material_item_id']]);
        $userMaterial = $userMatStmt->fetch(PDO::FETCH_ASSOC);

        if (!$userMaterial || $userMaterial['quantity'] < $material['quantity_required']) {
            $pdo->rollBack();
            echo json_encode(['success' => false, 'message' => "材料不足: {$material['material_name']}"]);
            exit;
        }

        // Consume materials
        $newQuantity = $userMaterial['quantity'] - $material['quantity_required'];
        if ($newQuantity <= 0) {
            // Remove item completely
            $deleteStmt = $pdo->prepare("DELETE FROM user_inventory WHERE id = :id");
            $deleteStmt->execute([':id' => $userMaterial['id']]);
        } else {
            // Update quantity
            $updateStmt = $pdo->prepare("UPDATE user_inventory SET quantity = :quantity WHERE id = :id");
            $updateStmt->execute([':quantity' => $newQuantity, ':id' => $userMaterial['id']]);
        }
    }

    // Start alchemy process
    $completionTime = new DateTime();
    $completionTime->add(new DateInterval('PT' . $recipe['craft_time_seconds'] . 'S'));

    $startAlchemyStmt = $pdo->prepare("
        INSERT INTO user_alchemy_progress (user_id, recipe_id, started_at, completion_time, is_completed)
        VALUES (:user_id, :recipe_id, NOW(), :completion_time, false)
    ");
    $startAlchemyStmt->execute([
        ':user_id' => $user_id,
        ':recipe_id' => $recipe_id,
        ':completion_time' => $completionTime->format('Y-m-d H:i:s')
    ]);

    $pdo->commit();

    echo json_encode([
        'success' => true,
        'message' => "開始煉製 {$recipe['result_item_name']}",
        'recipe_name' => $recipe['name'],
        'completion_time' => $completionTime->format('Y-m-d H:i:s'),
        'craft_time_seconds' => $recipe['craft_time_seconds']
    ]);

} catch (PDOException $e) {
    $pdo->rollBack();
    error_log("Start alchemy error: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Database error']);
}
?>
