<?php
// public/api/buy_skill.php

session_start();

require_once 'db_connect.php';

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$data = json_decode(file_get_contents('php://input'), true);
$skill_id = isset($data['skill_id']) ? (int)$data['skill_id'] : 0;

if ($skill_id > 0) {
    $user_id = $_SESSION['user_id'];

    // Get skill details
    $stmt_skill = $pdo->prepare("SELECT * FROM skills WHERE id = :skill_id;");
    $stmt_skill->execute([':skill_id' => $skill_id]);
    $skill = $stmt_skill->fetch(PDO::FETCH_ASSOC);

    // Get user's money
    $stmt_user = $pdo->prepare("SELECT money FROM users WHERE id = :user_id;");
    $stmt_user->execute([':user_id' => $user_id]);
    $user = $stmt_user->fetch(PDO::FETCH_ASSOC);

    if ($skill && $user['money'] >= $skill['cost']) {
        // Deduct cost
        $new_money = $user['money'] - $skill['cost'];
        $stmt_update_money = $pdo->prepare("UPDATE users SET money = :new_money WHERE id = :user_id;");
        $stmt_update_money->execute([':new_money' => $new_money, ':user_id' => $user_id]);

        // Add skill to user
        $stmt_add_skill = $pdo->prepare("INSERT INTO user_skills (user_id, skill_id) VALUES (:user_id, :skill_id);");
        $stmt_add_skill->execute([':user_id' => $user_id, ':skill_id' => $skill_id]);

        // Apply skill effect
        // For now, we'll just return success and let the frontend handle the effect
        
        // Fetch updated user info
        $stmt_user = $pdo->prepare("SELECT * FROM users WHERE id = :user_id");
        $stmt_user->execute([':user_id' => $user_id]);
        $user = $stmt_user->fetch(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'message' => 'Skill purchased successfully',
            'experience' => $user['experience'],
            'money' => $user['money'],
            'level' => $user['level'],
            'level_up_threshold' => $user['level_up_threshold'],
            'level_up' => false // No level up from buying skills in this logic
        ]);
    } else {
        echo json_encode(['success' => false, 'message' => 'Not enough money or skill not found']);
    }
} else {
    echo json_encode(['success' => false, 'message' => 'Invalid skill ID']);
}
?>