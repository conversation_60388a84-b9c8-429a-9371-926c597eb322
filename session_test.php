<?php
// session_test.php - Test session functionality

session_start();
header('Content-Type: application/json');

echo json_encode([
    'session_id' => session_id(),
    'user_id' => $_SESSION['user_id'] ?? null,
    'clan_id' => $_SESSION['clan_id'] ?? null,
    'session_data' => $_SESSION,
    'cookies' => $_COOKIE,
    'server_info' => [
        'REQUEST_METHOD' => $_SERVER['REQUEST_METHOD'],
        'HTTP_HOST' => $_SERVER['HTTP_HOST'],
        'REQUEST_URI' => $_SERVER['REQUEST_URI'],
        'HTTP_USER_AGENT' => $_SERVER['HTTP_USER_AGENT'] ?? 'Not set'
    ]
], JSON_PRETTY_PRINT);
?>
