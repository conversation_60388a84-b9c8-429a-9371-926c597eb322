<?php
// public/login.php

// Security: Set secure session parameters
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', 0); // Set to 1 if using HTTPS
ini_set('session.use_strict_mode', 1);

session_start();

require_once './api/db_connect.php';

function assignDailyMissions($pdo, $user_id) {
    try {
        // Get all missions
        $stmt_missions = $pdo->query("SELECT id FROM missions;");
        $missions = $stmt_missions->fetchAll(PDO::FETCH_COLUMN, 0);

        foreach ($missions as $mission_id) {
            // Check if user already has this mission for today
            $stmt_check = $pdo->prepare("
                SELECT id FROM user_missions
                WHERE user_id = :user_id AND mission_id = :mission_id AND assigned_at = CURRENT_DATE;
            ");
            $stmt_check->execute([':user_id' => $user_id, ':mission_id' => $mission_id]);

            if ($stmt_check->rowCount() == 0) {
                // Assign the mission
                $stmt_assign = $pdo->prepare("
                    INSERT INTO user_missions (user_id, mission_id, assigned_at)
                    VALUES (:user_id, :mission_id, CURRENT_DATE);
                ");
                $stmt_assign->execute([':user_id' => $user_id, ':mission_id' => $mission_id]);
            }
        }
    } catch (PDOException $e) {
        error_log("Error assigning daily missions: " . $e->getMessage());
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = $_POST['password'];

    $stmt = $pdo->prepare("SELECT id, password_hash, clan_id FROM users WHERE username = :username");
    $stmt->execute([':username' => $username]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($user && password_verify($password, $user['password_hash'])) {
        $_SESSION['user_id'] = $user['id'];
        if (!empty($user['clan_id'])) {
            $_SESSION['clan_id'] = $user['clan_id'];
        }

        // Assign daily missions if not already assigned
        assignDailyMissions($pdo, $user['id']);

        header('Location: index.php');
        exit;
    } else {
        $error = "用戶名或密碼錯誤";
    }
}
?>

<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>登入</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <h1>登入</h1>
        <?php if (isset($error)): ?>
            <p class="error"><?php echo $error; ?></p>
        <?php endif; ?>
        <form action="login.php" method="post">
            <div class="form-group">
                <label for="username">用戶名</label>
                <input type="text" name="username" id="username" required>
            </div>
            <div class="form-group">
                <label for="password">密碼</label>
                <input type="password" name="password" id="password" required>
            </div>
            <button type="submit">登入</button>
        </form>
        <p>還沒有帳號？ <a href="register.php">立即註冊</a></p>
    </div>
</body>
</html>