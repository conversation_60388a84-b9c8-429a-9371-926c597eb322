<?php
// public/index.php

session_start();

require_once './api/db_connect.php';

// Basic routing
$page = isset($_GET['page']) ? $_GET['page'] : 'game';

// If user is not logged in, redirect to login/register page
if (!isset($_SESSION['user_id']) && $page !== 'login' && $page !== 'register') {
    header('Location: login.php');
    exit;
}

// If user is logged in but has not chosen a clan, redirect to clan selection
if (isset($_SESSION['user_id']) && !isset($_SESSION['clan_id'])) {
    $stmt = $pdo->prepare("SELECT clan_id FROM users WHERE id = :user_id");
    $stmt->execute([':user_id' => $_SESSION['user_id']]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    if (empty($user['clan_id'])) {
        header('Location: templates/clan_selection.php');
        exit;
    } else {
        $_SESSION['clan_id'] = $user['clan_id'];
    }
}


switch ($page) {
    case 'game':
        include './templates/game.php';
        break;
    case 'clan_selection':
        include './templates/clan_selection.php';
        break;
    // Add other pages like login, register, etc.
    default:
        include './templates/game.php';
        break;
}
?>