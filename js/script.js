// public/js/script.js

document.addEventListener('DOMContentLoaded', () => {
    // --- STATE ---
    let gameState = {
        merit: 0,
        meritPerSecond: 0,
        clickPower: 1,
        level: 1,
        experience: 0,
        levelUpThreshold: 100,
        prestigePoints: 0,
        buildings: [],
        upgrades: [],
        missions: [],
        achievements: [],
        stats: {},
        // New features
        inventory: {
            all: [],
            material: [],
            equipment: [],
            consumable: [],
            treasure: []
        },
        inventoryStats: {
            total_items: 0,
            item_types: 0,
            total_value: 0
        },
        currentInventoryFilter: 'all',
        // Gacha system
        gachaPools: [],
        gachaHistory: [],
        // Alchemy system
        alchemyRecipes: [],
        alchemyProgress: [],
        // Exploration system
        explorationAreas: [],
        explorationProgress: [],
        userEnergy: { current: 100, max: 100 }
    };

    // --- ELEMENTS ---
    const mokugyo = document.getElementById('mokugyo');
    const plusOne = document.getElementById('plus-one');
    const goldenMokugyo = document.getElementById('golden-mokugyo');
    const meritScoreDisplay = document.getElementById('merit-score-display');
    const meritPerSecondDisplay = document.getElementById('merit-per-second-display');
    const clickSound = document.getElementById('click-sound');
    const levelUpSound = document.getElementById('level-up-sound');

    // Debug element selection
    console.log('🔍 Element selection debug:');
    console.log('mokugyo element:', mokugyo);
    console.log('meritScoreDisplay element:', meritScoreDisplay);
    console.log('plusOne element:', plusOne);

    // --- TABS ---
    const allTabs = document.querySelectorAll('.tab-link');
    const allTabContents = document.querySelectorAll('.tab-content');

    allTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTabId = tab.dataset.tab;
            const currentTabContainer = tab.closest('.tabs-panel, .right-panel');

            currentTabContainer.querySelectorAll('.tab-link').forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            currentTabContainer.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
            document.getElementById(targetTabId).classList.add('active');
        });
    });

    // --- NUMBER FORMATTING ---
    function formatNumber(num) {
        if (num < 1e6) return num.toLocaleString('en-US');
        if (num >= 1e100) return num.toExponential(2);
        const units = ["M", "B", "T", "Qa", "Qi", "Sx", "Sp", "Oc", "No", "Dc"];
        const order = Math.floor(Math.log10(Math.abs(num)) / 3);
        const unitname = units[Math.floor(order / 10)];
        const value = (num / Math.pow(10, order * 3)).toFixed(2);
        return `${value} ${unitname || ''}`;
    }

    // --- GAME LOGIC ---
    function mokugyoClick(e) {
        console.log('🥁 Mokugyo clicked! Event:', e); // Debug log

        // Visual feedback - change mokugyo briefly
        if (mokugyo) {
            mokugyo.style.transform = 'scale(0.95)';
            setTimeout(() => {
                mokugyo.style.transform = 'scale(1)';
            }, 100);
        }

        if (clickSound) {
            clickSound.currentTime = 0;
            clickSound.play();
        }

        const clickPower = calculateClickPower();
        console.log('💪 Click power calculated:', clickPower); // Debug log

        // Don't update local merit - wait for server response
        // gameState.merit += clickPower;
        // updateScore();

        // Animation
        const plusOneClone = plusOne.cloneNode(true);
        plusOneClone.textContent = `功德+${formatNumber(clickPower)}`;

        // Handle both mouse and touch events for positioning
        let clientX, clientY;
        if (e.touches && e.touches.length > 0) {
            // Touch event
            clientX = e.touches[0].clientX;
            clientY = e.touches[0].clientY;
        } else if (e.changedTouches && e.changedTouches.length > 0) {
            // Touch end event
            clientX = e.changedTouches[0].clientX;
            clientY = e.changedTouches[0].clientY;
        } else {
            // Mouse event
            clientX = e.clientX;
            clientY = e.clientY;
        }

        const rect = mokugyo.getBoundingClientRect();
        plusOneClone.style.left = `${clientX - rect.left}px`;
        plusOneClone.style.top = `${clientY - rect.top}px`;
        plusOneClone.classList.add('animate');
        mokugyo.parentElement.appendChild(plusOneClone);
        plusOneClone.addEventListener('animationend', () => plusOneClone.remove());

        // API Update
        console.log('📡 Sending API request to update_score.php...'); // Debug log

        // Visual indicator - add loading class
        if (mokugyo) {
            mokugyo.classList.add('loading');
        }

        fetch('api/update_score.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ increment: 1 }) // Send base click
        }).then(res => {
            console.log('📡 API response status:', res.status, 'OK:', res.ok); // Debug log
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            return res.json();
        }).then(data => {
            console.log('API response data:', data); // Debug log
            if (data.success) {
                // Update local state with server response
                if (data.money !== undefined) {
                    console.log('Previous merit:', gameState.merit, 'New merit from server:', data.money);
                    gameState.merit = data.money;
                    updateScore();
                    console.log('Merit display updated to:', gameState.merit); // Debug log
                }

                // Update player info if level up occurred or experience changed
                if (data.experience !== undefined || data.level_up || data.new_level) {
                    updatePlayerInfo({
                        level: data.new_level || gameState.level,
                        experience: data.experience || gameState.experience,
                        level_up_threshold: data.level_up_threshold || gameState.levelUpThreshold
                    });

                    // Show level up notification
                    if (data.level_up) {
                        showLevelUpNotification(data.new_level);
                    }
                }
            } else {
                console.error('Update score failed:', data.message);
                alert('點擊失敗: ' + data.message); // Show error to user
            }
        }).catch(error => {
            console.error('API call failed:', error);
            alert('網絡錯誤: ' + error.message); // Show error to user
        }).finally(() => {
            // Remove loading indicator
            if (mokugyo) {
                mokugyo.classList.remove('loading');
            }
        });
    }

    function calculateClickPower() {
        // Base click power
        let basePower = gameState.clickPower || 1;

        // Calculate multiplier from upgrades
        let multiplier = 1;
        if (gameState.upgrades) {
            gameState.upgrades.forEach(upgrade => {
                if (upgrade.owned && upgrade.effect_type === 'click_multiplier') {
                    multiplier *= upgrade.effect_value;
                }
            });
        }

        // Add prestige bonus
        let prestigeBonus = 1 + (gameState.prestigePoints * 0.01);

        return Math.floor(basePower * multiplier * prestigeBonus);
    }

    function updateScore() {
        const newValue = formatNumber(Math.floor(gameState.merit));
        console.log('📊 Updating score display:', gameState.merit, '→', newValue);
        if (meritScoreDisplay) {
            meritScoreDisplay.textContent = newValue;
            console.log('✅ Score display updated to:', meritScoreDisplay.textContent);
        } else {
            console.error('❌ meritScoreDisplay element not found!');
        }
    }

    // --- PLAYER INFO UPDATES ---
    function updatePlayerInfo(userData) {
        // Update level display
        const levelDisplay = document.getElementById('level-display');
        if (levelDisplay && userData.level !== undefined) {
            levelDisplay.textContent = userData.level;
            gameState.level = userData.level;
        }

        // Update experience bar
        const xpBar = document.getElementById('xp-bar');
        const xpText = document.getElementById('xp-text');
        if (xpBar && xpText && userData.experience !== undefined && userData.level_up_threshold !== undefined) {
            const xpPercentage = (userData.experience / userData.level_up_threshold) * 100;
            xpBar.style.width = Math.min(xpPercentage, 100) + '%';
            xpText.textContent = `${userData.experience} / ${userData.level_up_threshold}`;
            gameState.experience = userData.experience;
            gameState.levelUpThreshold = userData.level_up_threshold;
        }

        // Update prestige points
        const prestigeDisplay = document.getElementById('prestige-points-display');
        if (prestigeDisplay && userData.prestige_points !== undefined) {
            prestigeDisplay.textContent = userData.prestige_points;
            gameState.prestigePoints = userData.prestige_points;
        }
    }

    // --- LEADERBOARD UPDATES ---
    function updateLeaderboard(showLoading = false) {
        console.log('🏆 Updating leaderboard...');

        // Add visual loading indicator
        const leaderboardTable = document.getElementById('leaderboard-table');
        if (showLoading && leaderboardTable) {
            const tbody = leaderboardTable.querySelector('tbody');
            if (tbody) {
                tbody.style.opacity = '0.6';
            }
        }

        // Add cache-busting parameter
        const timestamp = Date.now();
        fetch(`api/get_clan_scores.php?t=${timestamp}`)
            .then(res => res.json())
            .then(data => {
                console.log('🏆 Leaderboard data received:', data);
                if (data.success) {
                    if (leaderboardTable) {
                        const tbody = leaderboardTable.querySelector('tbody');
                        if (tbody) {
                            tbody.innerHTML = '';
                            data.scores.forEach((score, index) => {
                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${index + 1}</td>
                                    <td>${score.name}</td>
                                    <td>${formatNumber(score.total_marks)}</td>
                                `;
                                tbody.appendChild(row);
                            });
                            tbody.style.opacity = '1'; // Remove loading effect
                            console.log('🏆 Leaderboard updated successfully');
                        }
                    }
                } else {
                    console.error('Leaderboard update failed:', data.message);
                }
            })
            .catch(error => {
                console.error('Failed to update leaderboard:', error);
                // Remove loading effect even on error
                if (leaderboardTable) {
                    const tbody = leaderboardTable.querySelector('tbody');
                    if (tbody) {
                        tbody.style.opacity = '1';
                    }
                }
            });
    }

    function updateMeritPerSecond() {
        let baseMps = 0;
        gameState.buildings.forEach(building => {
            baseMps += building.level * building.base_production;
        });
        
        // TODO: Apply multipliers from upgrades/prestige
        gameState.meritPerSecond = baseMps;
        meritPerSecondDisplay.textContent = `每秒功德：${formatNumber(gameState.meritPerSecond)}`;
    }

    

    function renderAll() {
        renderBuildings();
        renderUpgrades();
        renderMissions();
        renderAchievements();
        renderStats();
        renderInventory();
        renderGacha();
        renderAlchemy();
        renderExploration();
        updateMeritPerSecond();
        updateScore();
    }

    // === NEW FEATURES RENDERING ===

    // Inventory System
    function renderInventory() {
        const container = document.getElementById('inventory-grid');
        const statsCount = document.getElementById('inventory-count');
        const statsValue = document.getElementById('inventory-value');

        if (!container) return;

        container.innerHTML = '';

        // Update stats
        if (statsCount) {
            statsCount.textContent = `${gameState.inventoryStats.total_items}/100`;
        }
        if (statsValue) {
            statsValue.textContent = `總價值: ${formatNumber(gameState.inventoryStats.total_value)}`;
        }

        // Get current filter
        const currentItems = gameState.inventory[gameState.currentInventoryFilter] || [];

        currentItems.forEach(item => {
            const itemDiv = document.createElement('div');
            itemDiv.className = `inventory-item rarity-${item.rarity}`;
            itemDiv.innerHTML = `
                <div class="item-icon">${getItemIcon(item.type)}</div>
                <div class="item-name">${item.name}</div>
                ${item.quantity > 1 ? `<div class="item-quantity">${item.quantity}</div>` : ''}
            `;

            // Add tooltip and click events
            itemDiv.addEventListener('mouseenter', (e) => showItemTooltip(e, item));
            itemDiv.addEventListener('mouseleave', hideItemTooltip);
            itemDiv.addEventListener('click', () => showItemActions(item));

            container.appendChild(itemDiv);
        });

        // Add empty slots for visual effect
        const emptySlots = Math.max(0, 20 - currentItems.length);
        for (let i = 0; i < emptySlots; i++) {
            const emptyDiv = document.createElement('div');
            emptyDiv.className = 'inventory-item empty';
            emptyDiv.style.opacity = '0.3';
            emptyDiv.style.border = '2px dashed var(--border-color)';
            container.appendChild(emptyDiv);
        }
    }

    function getItemIcon(type) {
        const icons = {
            'material': '🌿',
            'equipment': '⚔️',
            'consumable': '💊',
            'treasure': '💎'
        };
        return icons[type] || '📦';
    }

    let currentTooltip = null;

    function showItemTooltip(e, item) {
        hideItemTooltip(); // Remove any existing tooltip

        const tooltip = document.createElement('div');
        tooltip.className = 'item-tooltip';
        tooltip.innerHTML = `
            <div class="tooltip-name rarity-${item.rarity}">${item.name}</div>
            <div class="tooltip-description">${item.description}</div>
            <div class="tooltip-stats">
                <div>類型: ${getTypeDisplayName(item.type)}</div>
                <div>稀有度: ${getRarityDisplayName(item.rarity)}</div>
                <div>數量: ${item.quantity}/${item.max_stack}</div>
                <div>售價: ${formatNumber(item.sell_price)} 功德</div>
            </div>
        `;

        document.body.appendChild(tooltip);
        currentTooltip = tooltip;

        // Position tooltip
        const rect = e.target.getBoundingClientRect();
        tooltip.style.position = 'fixed';
        tooltip.style.left = (rect.right + 10) + 'px';
        tooltip.style.top = rect.top + 'px';

        // Adjust if tooltip goes off screen
        const tooltipRect = tooltip.getBoundingClientRect();
        if (tooltipRect.right > window.innerWidth) {
            tooltip.style.left = (rect.left - tooltipRect.width - 10) + 'px';
        }
        if (tooltipRect.bottom > window.innerHeight) {
            tooltip.style.top = (window.innerHeight - tooltipRect.height - 10) + 'px';
        }
    }

    function hideItemTooltip() {
        if (currentTooltip) {
            currentTooltip.remove();
            currentTooltip = null;
        }
    }

    function getTypeDisplayName(type) {
        const names = {
            'material': '材料',
            'equipment': '裝備',
            'consumable': '消耗品',
            'treasure': '寶物'
        };
        return names[type] || type;
    }

    function getRarityDisplayName(rarity) {
        const names = {
            'common': '普通',
            'uncommon': '優秀',
            'rare': '稀有',
            'epic': '史詩',
            'legendary': '傳說'
        };
        return names[rarity] || rarity;
    }

    function showItemActions(item) {
        const actions = [];

        // Add sell action for all items
        if (item.sell_price > 0) {
            actions.push(`出售 (${formatNumber(item.sell_price)} 功德)`);
        }

        // Add use action for consumables
        if (item.type === 'consumable') {
            actions.push('使用');
        }

        // Add equip action for equipment
        if (item.type === 'equipment') {
            actions.push('裝備');
        }

        if (actions.length === 0) {
            return;
        }

        // Simple action selection for now - just sell
        if (confirm(`${item.name}\n\n選擇動作:\n1. 出售 (${formatNumber(item.sell_price)} 功德)\n\n確定要出售嗎？`)) {
            sellItem(item.inventory_id, 1);
        }
    }

    // Gacha System
    function renderGacha() {
        const container = document.getElementById('gacha-pools');
        const historyContainer = document.getElementById('gacha-history-list');

        if (!container) return;

        container.innerHTML = '';

        gameState.gachaPools.forEach(pool => {
            const poolDiv = document.createElement('div');
            poolDiv.className = 'gacha-pool';

            // Featured items display
            const featuredItems = pool.featured_items || [];
            const featuredHtml = featuredItems.length > 0 ?
                `<div class="gacha-featured-items">
                    <span style="color: var(--text-light); font-size: 0.8em; margin-right: 5px;">精選:</span>
                    ${featuredItems.map(item => `<span class="featured-item">${item.name}</span>`).join('')}
                </div>` : '';

            poolDiv.innerHTML = `
                <div class="gacha-pool-header">
                    <div class="gacha-pool-name">${pool.name}</div>
                    <div class="gacha-pool-cost">${formatNumber(pool.cost_amount)} 功德</div>
                </div>
                <div class="gacha-pool-description">${pool.description}</div>
                ${featuredHtml}
                <div class="gacha-pull-buttons">
                    <button class="gacha-pull-btn" onclick="performGacha(${pool.id}, 1)"
                            ${gameState.merit < pool.cost_amount ? 'disabled' : ''}>
                        單抽 (${formatNumber(pool.cost_amount)})
                    </button>
                    <button class="gacha-pull-btn" onclick="performGacha(${pool.id}, 10)"
                            ${gameState.merit < (pool.cost_amount * 10) ? 'disabled' : ''}>
                        十連抽 (${formatNumber(pool.cost_amount * 10)})
                    </button>
                </div>
            `;

            container.appendChild(poolDiv);
        });

        // Render history
        if (historyContainer) {
            historyContainer.innerHTML = '';
            gameState.gachaHistory.forEach(historyItem => {
                const historyDiv = document.createElement('div');
                historyDiv.className = 'history-item';
                historyDiv.innerHTML = `
                    <div class="history-item-icon">${getItemIcon(historyItem.rarity)}</div>
                    <div class="history-item-info">
                        <span class="rarity-${historyItem.rarity}">${historyItem.item_name}</span>
                        ${historyItem.quantity > 1 ? ` x${historyItem.quantity}` : ''}
                        <div style="font-size: 0.7em; color: var(--text-dark);">${historyItem.pool_name}</div>
                    </div>
                    <div class="history-item-time">${formatTime(historyItem.pulled_at)}</div>
                `;
                historyContainer.appendChild(historyDiv);
            });
        }
    }

    function formatTime(timestamp) {
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;

        if (diff < 60000) return '剛剛';
        if (diff < 3600000) return Math.floor(diff / 60000) + '分鐘前';
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小時前';
        return Math.floor(diff / 86400000) + '天前';
    }

    // Alchemy System
    function renderAlchemy() {
        const progressContainer = document.getElementById('alchemy-progress');
        const recipesContainer = document.getElementById('alchemy-recipes');

        if (!progressContainer || !recipesContainer) return;

        // Render current progress
        progressContainer.innerHTML = '';
        if (gameState.alchemyProgress.length === 0) {
            progressContainer.innerHTML = '<div class="alchemy-progress empty">目前沒有進行中的煉丹</div>';
        } else {
            gameState.alchemyProgress.forEach(progress => {
                const progressDiv = document.createElement('div');
                progressDiv.className = 'progress-item';

                const isReady = progress.remaining_seconds <= 0;
                const timeDisplay = isReady ? '完成！' : formatTime(progress.remaining_seconds * 1000);

                progressDiv.innerHTML = `
                    <div class="progress-item-icon">${getItemIcon('consumable')}</div>
                    <div class="progress-item-info">
                        <div class="progress-item-name">${progress.recipe_name}</div>
                        <div class="progress-item-time">煉製中...</div>
                    </div>
                    <div class="progress-timer ${isReady ? 'ready' : ''}">${timeDisplay}</div>
                    <button class="collect-btn" onclick="collectAlchemy(${progress.id})"
                            ${!isReady ? 'disabled' : ''}>
                        ${isReady ? '收取' : '等待中'}
                    </button>
                `;

                progressContainer.appendChild(progressDiv);
            });
        }

        // Render recipes
        recipesContainer.innerHTML = '';
        gameState.alchemyRecipes.forEach(recipe => {
            const recipeDiv = document.createElement('div');
            recipeDiv.className = `recipe-card ${!recipe.can_craft ? 'disabled' : ''}`;

            // Materials HTML
            const materialsHtml = recipe.materials.map(material => {
                const sufficient = material.has_enough;
                return `
                    <div class="material-item ${sufficient ? 'sufficient' : 'insufficient'}">
                        <span class="material-icon">${getItemIcon('material')}</span>
                        <span class="material-name">${material.material_name}</span>
                        <span class="material-quantity ${sufficient ? 'sufficient' : 'insufficient'}">
                            ${material.user_quantity}/${material.quantity_required}
                        </span>
                    </div>
                `;
            }).join('');

            const craftTimeDisplay = formatCraftTime(recipe.craft_time_seconds);
            const successRateDisplay = Math.round(recipe.success_rate * 100);

            recipeDiv.innerHTML = `
                <div class="recipe-header">
                    <div class="recipe-name">${recipe.name}</div>
                    <div class="recipe-level-req ${!recipe.level_requirement_met ? 'not-met' : ''}">
                        Lv.${recipe.required_level}
                    </div>
                </div>
                <div class="recipe-result">
                    <div class="recipe-result-icon">${getItemIcon('consumable')}</div>
                    <div class="recipe-result-info">
                        <div class="recipe-result-name">${recipe.result_item_name}</div>
                        <div class="recipe-result-stats">數量: ${recipe.result_quantity}</div>
                    </div>
                </div>
                <div class="recipe-materials">
                    <h5>所需材料:</h5>
                    <div class="materials-list">${materialsHtml}</div>
                </div>
                <div class="recipe-info">
                    <span>製作時間: ${craftTimeDisplay}</span>
                    <span>成功率: ${successRateDisplay}%</span>
                </div>
                <button class="recipe-craft-btn" onclick="startAlchemy(${recipe.id})"
                        ${!recipe.can_craft ? 'disabled' : ''}>
                    ${!recipe.level_requirement_met ? '等級不足' :
                      !recipe.can_craft ? '材料不足' : '開始煉製'}
                </button>
            `;

            recipesContainer.appendChild(recipeDiv);
        });
    }

    function formatCraftTime(seconds) {
        if (seconds < 60) return seconds + '秒';
        if (seconds < 3600) return Math.floor(seconds / 60) + '分鐘';
        return Math.floor(seconds / 3600) + '小時';
    }

    // Exploration System
    function renderExploration() {
        const energyDisplay = document.getElementById('energy-display');
        const progressContainer = document.getElementById('exploration-progress');
        const areasContainer = document.getElementById('exploration-areas');

        if (!energyDisplay || !progressContainer || !areasContainer) return;

        // Update energy display
        energyDisplay.textContent = `${gameState.userEnergy.current}/${gameState.userEnergy.max}`;

        // Render current progress
        progressContainer.innerHTML = '';
        if (gameState.explorationProgress.length === 0) {
            progressContainer.innerHTML = '<div class="exploration-progress empty">目前沒有進行中的探索</div>';
        } else {
            gameState.explorationProgress.forEach(progress => {
                const progressDiv = document.createElement('div');
                progressDiv.className = 'exploration-progress-item';

                const isReady = progress.remaining_seconds <= 0;
                const timeDisplay = isReady ? '完成！' : formatTime(progress.remaining_seconds * 1000);

                progressDiv.innerHTML = `
                    <div class="exploration-progress-icon">🗺️</div>
                    <div class="exploration-progress-info">
                        <div class="exploration-progress-name">${progress.area_name}</div>
                        <div class="exploration-progress-time">探索中...</div>
                    </div>
                    <div class="exploration-timer ${isReady ? 'ready' : ''}">${timeDisplay}</div>
                    <button class="collect-exploration-btn" onclick="collectExploration(${progress.id})"
                            ${!isReady ? 'disabled' : ''}>
                        ${isReady ? '收取' : '等待中'}
                    </button>
                `;

                progressContainer.appendChild(progressDiv);
            });
        }

        // Render areas
        areasContainer.innerHTML = '';
        gameState.explorationAreas.forEach(area => {
            const areaDiv = document.createElement('div');
            let areaClass = 'exploration-area';
            if (!area.is_unlocked) areaClass += ' locked';
            else if (!area.can_explore) areaClass += ' no-energy';

            areaDiv.className = areaClass;

            // Rewards HTML
            const rewardsHtml = area.rewards.map(reward => {
                let rewardClass = 'reward-item';
                let rewardText = '';
                let rewardIcon = '';

                if (reward.reward_type === 'money') {
                    rewardClass += ' money';
                    rewardIcon = '💰';
                    rewardText = `${reward.money_amount}功德`;
                } else if (reward.reward_type === 'experience') {
                    rewardClass += ' experience';
                    rewardIcon = '⭐';
                    rewardText = `${reward.experience_amount}經驗`;
                } else if (reward.reward_type === 'item') {
                    rewardClass += ' item';
                    rewardIcon = getItemIcon(reward.item_rarity || 'common');
                    rewardText = reward.item_name || '物品';
                }

                const dropRate = Math.round(reward.drop_rate * 100);
                return `
                    <div class="${rewardClass}">
                        <span class="reward-icon">${rewardIcon}</span>
                        <span>${rewardText} (${dropRate}%)</span>
                    </div>
                `;
            }).join('');

            const exploreTimeDisplay = formatCraftTime(area.exploration_time_seconds);

            areaDiv.innerHTML = `
                <div class="area-header">
                    <div class="area-name">${area.name}</div>
                    <div class="area-level-req ${!area.is_unlocked ? 'not-met' : ''}">
                        Lv.${area.required_level}
                    </div>
                </div>
                <div class="area-description">${area.description}</div>
                <div class="area-stats">
                    <span>探索時間: ${exploreTimeDisplay}</span>
                    <span>體力消耗: ${area.energy_cost}</span>
                </div>
                <div class="area-rewards">
                    <h5>可能獲得:</h5>
                    <div class="rewards-list">${rewardsHtml}</div>
                </div>
                <button class="area-explore-btn" onclick="startExploration(${area.id})"
                        ${!area.can_explore ? 'disabled' : ''}>
                    ${!area.is_unlocked ? '等級不足' :
                      gameState.userEnergy.current < area.energy_cost ? '體力不足' : '開始探索'}
                </button>
            `;

            areasContainer.appendChild(areaDiv);
        });
    }

    function renderBuildings() {
        const container = document.getElementById('buildings-tab');
        container.innerHTML = '';
        gameState.buildings.forEach(b => {
            const cost = Math.floor(b.base_cost * Math.pow(b.cost_increase_ratio, b.level));
            const card = document.createElement('div');
            card.className = 'item-card';
            card.innerHTML = `
                <div class="item-info">
                    <h3>${b.name}</h3>
                    <p>功德: ${formatNumber(b.base_production * b.level)}/s</p>
                    <p>費用: ${formatNumber(cost)}</p>
                </div>
                <div class="item-level">${b.level}</div>
                <button class="buy-button" data-id="${b.id}" ${gameState.merit < cost ? 'disabled' : ''}>購買</button>
            `;
            container.appendChild(card);
        });
    }
    
    // Add renderUpgrades, renderMissions, etc. following the same pattern
    function renderUpgrades() {
        const container = document.getElementById('upgrades-tab');
        container.innerHTML = '';
        gameState.upgrades.forEach(u => {
            const card = document.createElement('div');
            card.className = 'item-card';
            card.innerHTML = `
                <div class="item-info">
                    <h3>${u.name}</h3>
                    <p>${u.description}</p>
                    <p>費用: ${formatNumber(u.cost)}</p>
                </div>
                <button class="buy-button" data-id="${u.id}" ${gameState.merit < u.cost ? 'disabled' : ''}>購買</button>
            `;
            container.appendChild(card);
        });
    }
    function renderMissions() {
        const container = document.getElementById('missions-tab');
        container.innerHTML = '';
        gameState.missions.forEach(m => {
            const card = document.createElement('div');
            card.className = 'item-card';
            const progress = m.progress > m.target ? m.target : m.progress;
            const isComplete = progress >= m.target;
            card.innerHTML = `
                <div class="item-info">
                    <h3>${m.name}</h3>
                    <p>${m.description}</p>
                    <p>進度: ${formatNumber(progress)} / ${formatNumber(m.target)}</p>
                </div>
                <button class="claim-button" data-id="${m.id}" ${!isComplete ? 'disabled' : ''}>領取</button>
            `;
            container.appendChild(card);
        });
    }
    function renderAchievements() {
        const container = document.getElementById('achievements-tab');
        container.innerHTML = '';
        gameState.achievements.forEach(a => {
            const card = document.createElement('div');
            card.className = 'item-card';
            const isUnlocked = a.unlocked; // Assuming this property is set
            card.innerHTML = `
                <div class="item-info">
                    <h3>${a.name}</h3>
                    <p>${a.description}</p>
                </div>
                <div class="status">${isUnlocked ? '已解鎖' : '未解鎖'}</div>
            `;
            container.appendChild(card);
        });
    }
    function renderStats() {
        const container = document.getElementById('stats-tab');
        container.innerHTML = `
            <p>總點擊次數: ${formatNumber(gameState.stats.total_clicks || 0)}</p>
            <p>總功德獲取量: ${formatNumber(gameState.stats.total_merit_earned || 0)}</p>
            <p>擁有建築物: ${formatNumber(gameState.stats.buildings_owned || 0)}</p>
            <p>已解鎖成就: ${formatNumber(gameState.stats.achievements_unlocked || 0)}</p>
        `;
    }


    // --- GOLDEN MOKUGYO ---
    function showGoldenMokugyo() {
        const x = Math.random() * 80 + 10;
        const y = Math.random() * 80 + 10;
        goldenMokugyo.style.left = `${x}%`;
        goldenMokugyo.style.top = `${y}%`;
        goldenMokugyo.style.display = 'block';
        setTimeout(() => {
            goldenMokugyo.style.display = 'none';
        }, 8000); // Visible for 8 seconds
    }

    function clickGoldenMokugyo() {
        fetch('api/golden_mokugyo.php').then(res => res.json()).then(data => {
            if (data.success) {
                gameState.merit += data.reward;
                alert(data.message);
                updateScore();
            }
        });
        goldenMokugyo.style.display = 'none';
    }

    // --- GAME LOOP ---
    function gameLoop() {
        // Only re-render buildings to update button states
        // Don't add passive income locally - server handles this
        renderBuildings();
    }

    // --- PASSIVE INCOME COLLECTION ---
    function collectPassiveIncome() {
        fetch('api/collect_passive_income.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(res => {
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            return res.json();
        })
        .then(data => {
            if (data.success && data.income_earned > 0) {
                console.log('Passive income collected:', data.income_earned);
                gameState.merit = data.money;
                updateScore();

                // Update player info if provided
                if (data.experience !== undefined) {
                    updatePlayerInfo({
                        level: data.level || gameState.level,
                        experience: data.experience,
                        level_up_threshold: data.level_up_threshold || gameState.levelUpThreshold
                    });
                }

                // Show notification if significant income was earned
                if (data.income_earned > 10) {
                    showPassiveIncomeNotification(data.income_earned);
                }
            } else if (!data.success) {
                console.error('Passive income collection failed:', data.message);
            }
        })
        .catch(error => {
            console.error('Passive income collection failed:', error);
        });
    }

    function showPassiveIncomeNotification(amount) {
        // Create a notification element
        const notification = document.createElement('div');
        notification.className = 'passive-income-notification';
        notification.textContent = `離線收益: 功德+${formatNumber(amount)}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideInRight 0.5s ease-out;
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.5s ease-in';
            setTimeout(() => notification.remove(), 500);
        }, 3000);
    }

    function showLevelUpNotification(newLevel) {
        // Play level up sound if available
        if (levelUpSound) {
            levelUpSound.currentTime = 0;
            levelUpSound.play();
        }

        // Create level up notification
        const notification = document.createElement('div');
        notification.className = 'level-up-notification';
        notification.textContent = `🎉 升級了! 等級 ${newLevel}`;
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            padding: 20px 30px;
            border-radius: 12px;
            font-weight: bold;
            font-size: 1.5em;
            z-index: 1001;
            box-shadow: 0 8px 20px rgba(0,0,0,0.4);
            animation: levelUpPulse 2s ease-out;
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'fadeOut 0.5s ease-in';
            setTimeout(() => notification.remove(), 500);
        }, 2500);
    }

    // --- EVENT LISTENERS ---
    // Add both click and touch events for better mobile support
    if (mokugyo) {
        console.log('🎯 Adding event listeners to mokugyo element');
        mokugyo.addEventListener('click', mokugyoClick);
        mokugyo.addEventListener('touchend', function(e) {
            e.preventDefault(); // Prevent double-firing on mobile
            mokugyoClick(e);
        });
        console.log('✅ Event listeners added successfully');
    } else {
        console.error('❌ Mokugyo element not found! Cannot add event listeners.');
    }

    goldenMokugyo.addEventListener('click', clickGoldenMokugyo);
    goldenMokugyo.addEventListener('touchend', function(e) {
        e.preventDefault();
        clickGoldenMokugyo();
    });

    document.getElementById('buildings-tab').addEventListener('click', e => {
        console.log('Buildings tab clicked:', e.target);
        if (e.target.classList.contains('buy-button')) {
            const buildingId = parseInt(e.target.dataset.id);
            console.log('Buy button clicked for building ID:', buildingId);
            buyBuilding(buildingId);
        }
    });

    document.getElementById('upgrades-tab').addEventListener('click', e => {
        if (e.target.classList.contains('buy-button')) {
            const upgradeId = parseInt(e.target.dataset.id);
            buyUpgrade(upgradeId);
        }
    });

    document.getElementById('missions-tab').addEventListener('click', e => {
        if (e.target.classList.contains('claim-button')) {
            const missionId = parseInt(e.target.dataset.id);
            claimMission(missionId);
        }
    });

    // Inventory filter event listeners
    document.addEventListener('click', e => {
        if (e.target.classList.contains('filter-btn')) {
            // Update active filter button
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            e.target.classList.add('active');

            // Update current filter and re-render
            gameState.currentInventoryFilter = e.target.dataset.filter;
            renderInventory();
        }
    });

    // --- BUY/CLAIM FUNCTIONS ---
    function buyBuilding(id) {
        // Prevent multiple clicks by disabling the button
        const button = document.querySelector(`#buildings-tab button[data-id="${id}"]`);
        if (button) {
            if (button.disabled) {
                console.log('Building purchase already in progress');
                return; // Prevent multiple clicks
            }
            button.disabled = true;
            button.textContent = '購買中...';
            button.style.opacity = '0.6';
        }

        console.log('🏗️ Buying building:', id);

        fetch('api/buy_building.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ building_id: id })
        })
        .then(res => {
            console.log('🏗️ Building purchase response status:', res.status);
            return res.json();
        })
        .then(data => {
            console.log('🏗️ Building purchase response:', data);
            if (data.success) {
                gameState.merit = data.new_money;
                updateScore(); // Update score display immediately

                // Reload building data more efficiently
                fetch('api/get_buildings.php')
                    .then(res => res.json())
                    .then(buildingsData => {
                        if (buildingsData.success) {
                            gameState.buildings = buildingsData.buildings;
                            renderBuildings();
                            updateMeritPerSecond();
                        }
                    });

                // Update leaderboard since clan total marks may have changed
                setTimeout(() => updateLeaderboard(true), 500);
            } else {
                console.error('Buy building failed:', data.message);
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Buy building API call failed:', error);
            alert('購買失敗，請稍後再試');
        })
        .finally(() => {
            // Re-enable the button
            if (button) {
                button.disabled = false;
                button.textContent = '購買';
                button.style.opacity = '1';
            }
        });
    }

    function buyUpgrade(id) {
        fetch('api/buy_skill.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ skill_id: id })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                gameState.merit = data.money;
                loadInitialData();
                renderUpgrades();
            } else {
                alert(data.message);
            }
        });
    }

    function claimMission(id) {
        fetch('api/claim_mission_reward.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ user_mission_id: id })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                gameState.merit = data.money;
                gameState.experience = data.experience;

                // Update player info with new experience/level
                updatePlayerInfo({
                    level: data.level || gameState.level,
                    experience: data.experience,
                    level_up_threshold: data.level_up_threshold || gameState.levelUpThreshold
                });

                loadInitialData();
                renderMissions();

                // Update leaderboard since merit may have changed
                setTimeout(() => updateLeaderboard(true), 500);
            } else {
                alert(data.message);
            }
        });
    }

    // === GACHA API FUNCTIONS ===

    function loadGachaPools() {
        fetch('api/get_gacha_pools.php')
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    gameState.gachaPools = data.pools;
                    gameState.gachaHistory = data.recent_history;
                    renderGacha();
                } else {
                    console.error('Failed to load gacha pools:', data.message);
                }
            })
            .catch(error => {
                console.error('Gacha pools load error:', error);
            });
    }

    window.performGacha = function(poolId, pullCount) {
        const buttons = document.querySelectorAll('.gacha-pull-btn');
        buttons.forEach(btn => {
            btn.disabled = true;
            btn.classList.add('pulling');
            btn.textContent = '抽獎中...';
        });

        fetch('api/perform_gacha.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                pool_id: poolId,
                pull_count: pullCount
            })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                // Update money
                gameState.merit = data.new_money;
                updateScore();

                // Show results
                showGachaResults(data.results, data.pool_name);

                // Reload data
                loadGachaPools();
                loadInventory();

                showNotification(`${data.pool_name} 抽獎完成！`, 'success');
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Gacha pull error:', error);
            showNotification('抽獎失敗', 'error');
        })
        .finally(() => {
            // Re-enable buttons
            setTimeout(() => {
                buttons.forEach(btn => {
                    btn.disabled = false;
                    btn.classList.remove('pulling');
                });
                renderGacha(); // This will restore proper button text and states
            }, 1000);
        });
    };

    function showGachaResults(results, poolName) {
        const resultsContainer = document.getElementById('gacha-results');
        const resultsGrid = document.getElementById('results-grid');

        if (!resultsContainer || !resultsGrid) return;

        resultsGrid.innerHTML = '';

        results.forEach((result, index) => {
            setTimeout(() => {
                const resultDiv = document.createElement('div');
                resultDiv.className = `result-item rarity-${result.rarity} new`;
                resultDiv.innerHTML = `
                    <div class="result-item-icon">${getItemIcon(result.type)}</div>
                    <div class="result-item-name">${result.name}</div>
                    <div class="result-item-quantity">x${result.quantity}</div>
                `;
                resultsGrid.appendChild(resultDiv);
            }, index * 200); // Stagger the animations
        });

        resultsContainer.style.display = 'block';
    }

    window.closeGachaResults = function() {
        const resultsContainer = document.getElementById('gacha-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    };

    // === ALCHEMY API FUNCTIONS ===

    function loadAlchemyRecipes() {
        fetch('api/get_alchemy_recipes.php')
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    gameState.alchemyRecipes = data.recipes;
                    gameState.alchemyProgress = data.current_progress;
                    renderAlchemy();

                    // Start timer for ongoing alchemy
                    startAlchemyTimer();
                } else {
                    console.error('Failed to load alchemy recipes:', data.message);
                }
            })
            .catch(error => {
                console.error('Alchemy recipes load error:', error);
            });
    }

    window.startAlchemy = function(recipeId) {
        const button = event.target;
        button.disabled = true;
        button.classList.add('crafting');
        button.textContent = '開始煉製...';

        fetch('api/start_alchemy.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ recipe_id: recipeId })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');

                // Reload data
                loadAlchemyRecipes();
                loadInventory();
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Start alchemy error:', error);
            showNotification('開始煉製失敗', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.classList.remove('crafting');
            // Text will be restored by renderAlchemy()
        });
    };

    window.collectAlchemy = function(progressId) {
        const button = event.target;
        button.disabled = true;
        button.textContent = '收取中...';

        fetch('api/collect_alchemy.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ progress_id: progressId })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                const messageType = data.is_craft_success ? 'success' : 'warning';
                showNotification(data.message, messageType);

                // Reload data
                loadAlchemyRecipes();
                loadInventory();
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Collect alchemy error:', error);
            showNotification('收取失敗', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = '收取';
        });
    };

    let alchemyTimer = null;

    function startAlchemyTimer() {
        if (alchemyTimer) {
            clearInterval(alchemyTimer);
        }

        alchemyTimer = setInterval(() => {
            let hasOngoing = false;

            gameState.alchemyProgress.forEach(progress => {
                if (progress.remaining_seconds > 0) {
                    progress.remaining_seconds--;
                    hasOngoing = true;
                }
            });

            if (hasOngoing) {
                renderAlchemy();
            } else {
                clearInterval(alchemyTimer);
                alchemyTimer = null;
            }
        }, 1000);
    }

    // === EXPLORATION API FUNCTIONS ===

    function loadExplorationAreas() {
        fetch('api/get_exploration_areas.php')
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    gameState.explorationAreas = data.areas;
                    gameState.explorationProgress = data.current_progress;
                    gameState.userEnergy = data.user_energy;
                    renderExploration();

                    // Start timer for ongoing exploration
                    startExplorationTimer();
                } else {
                    console.error('Failed to load exploration areas:', data.message);
                }
            })
            .catch(error => {
                console.error('Exploration areas load error:', error);
            });
    }

    window.startExploration = function(areaId) {
        const button = event.target;
        button.disabled = true;
        button.classList.add('exploring');
        button.textContent = '開始探索...';

        fetch('api/start_exploration.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ area_id: areaId })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');

                // Reload data
                loadExplorationAreas();
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Start exploration error:', error);
            showNotification('開始探索失敗', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.classList.remove('exploring');
            // Text will be restored by renderExploration()
        });
    };

    window.collectExploration = function(progressId) {
        const button = event.target;
        button.disabled = true;
        button.textContent = '收取中...';

        fetch('api/collect_exploration.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ progress_id: progressId })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                showNotification(data.message, 'success');

                // Update money if received
                if (data.rewards.money > 0) {
                    gameState.merit += data.rewards.money;
                    updateScore();
                }

                // Reload data
                loadExplorationAreas();
                loadInventory();
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Collect exploration error:', error);
            showNotification('收取失敗', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.textContent = '收取';
        });
    };

    let explorationTimer = null;

    function startExplorationTimer() {
        if (explorationTimer) {
            clearInterval(explorationTimer);
        }

        explorationTimer = setInterval(() => {
            let hasOngoing = false;

            gameState.explorationProgress.forEach(progress => {
                if (progress.remaining_seconds > 0) {
                    progress.remaining_seconds--;
                    hasOngoing = true;
                }
            });

            // Also regenerate energy (1 per minute = 1 per 60 seconds)
            if (gameState.userEnergy.current < gameState.userEnergy.max) {
                // This is a simplified version - real regeneration happens server-side
                // This is just for display purposes
            }

            if (hasOngoing) {
                renderExploration();
            } else {
                clearInterval(explorationTimer);
                explorationTimer = null;
            }
        }, 1000);
    }

    // === INVENTORY API FUNCTIONS ===

    function loadInventory() {
        fetch('api/get_inventory.php')
            .then(res => res.json())
            .then(data => {
                if (data.success) {
                    gameState.inventory = data.inventory;
                    gameState.inventoryStats = data.stats;
                    renderInventory();
                } else {
                    console.error('Failed to load inventory:', data.message);
                }
            })
            .catch(error => {
                console.error('Inventory load error:', error);
            });
    }

    function sellItem(inventoryId, quantity) {
        fetch('api/sell_item.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
                inventory_id: inventoryId,
                quantity: quantity
            })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                // Update money
                gameState.merit = data.new_money;
                updateScore();

                // Reload inventory
                loadInventory();

                // Show notification
                showNotification(data.message, 'success');
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Sell item error:', error);
            showNotification('出售失敗', 'error');
        });
    }

    // Test function to give items
    window.giveTestItems = function() {
        fetch('api/give_test_items.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                loadInventory();
                showNotification(data.message, 'success');
                console.log('Items given:', data.items_given);
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Give test items error:', error);
            showNotification('獲得物品失敗', 'error');
        });
    };

    // --- DATA LOADING & RENDERING ---
    function loadInitialData() {
        Promise.all([
            fetch('api/get_buildings.php').then(res => res.json()),
            fetch('api/get_skills.php').then(res => res.json()),
            fetch('api/get_missions.php').then(res => res.json()),
            fetch('api/get_achievements.php').then(res => res.json()),
            fetch('api/get_stats.php').then(res => res.json()),
            fetch('api/get_inventory.php').then(res => res.json()),
            fetch('api/get_gacha_pools.php').then(res => res.json()),
            fetch('api/get_alchemy_recipes.php').then(res => res.json()),
            fetch('api/get_exploration_areas.php').then(res => res.json())
        ]).then(([buildingsData, skillsData, missionsData, achievementsData, statsData, inventoryData, gachaData, alchemyData, explorationData]) => {
            if (statsData.success) {
                // Get current money from user data, not total_merit_earned
                fetch('api/user.php').then(res => res.json()).then(userData => {
                    if (userData.success) {
                        gameState.merit = parseInt(userData.user.money) || 0;
                        updateScore(); // Update display with current merit

                        // Update player info display with level and experience
                        updatePlayerInfo({
                            level: userData.user.level,
                            experience: userData.user.experience,
                            level_up_threshold: userData.user.level_up_threshold,
                            prestige_points: userData.user.prestige_points
                        });
                    } else {
                        console.error('Failed to load user data:', userData.message);
                    }
                }).catch(error => {
                    console.error('Failed to fetch user data:', error);
                });
                gameState.stats = statsData.stats;
            } else {
                console.error('Failed to load stats:', statsData.message);
            }
            if (buildingsData.success) {
                gameState.buildings = buildingsData.all_buildings.map(b => {
                    b.level = buildingsData.user_buildings[b.id] || 0;
                    return b;
                });
            } else {
                console.error('Failed to load buildings:', buildingsData.message);
            }
            if (skillsData.success) {
                gameState.upgrades = skillsData.all_skills.map(s => {
                    s.owned = skillsData.user_skills.includes(s.id.toString());
                    return s;
                });
            } else {
                console.error('Failed to load skills:', skillsData.message);
            }
            if (missionsData.success) {
                gameState.missions = missionsData.missions;
            } else {
                console.error('Failed to load missions:', missionsData.message);
            }
            if (achievementsData.success) {
                gameState.achievements = achievementsData.all_achievements.map(a => {
                    a.unlocked = achievementsData.user_achievements[a.id];
                    return a;
                });
            } else {
                console.error('Failed to load achievements:', achievementsData.message);
            }
            if (inventoryData.success) {
                gameState.inventory = inventoryData.inventory;
                gameState.inventoryStats = inventoryData.stats;
            } else {
                console.error('Failed to load inventory:', inventoryData.message);
            }
            if (gachaData.success) {
                gameState.gachaPools = gachaData.pools;
                gameState.gachaHistory = gachaData.recent_history;
            } else {
                console.error('Failed to load gacha data:', gachaData.message);
            }
            if (alchemyData.success) {
                gameState.alchemyRecipes = alchemyData.recipes;
                gameState.alchemyProgress = alchemyData.current_progress;
            } else {
                console.error('Failed to load alchemy data:', alchemyData.message);
            }
            if (explorationData.success) {
                gameState.explorationAreas = explorationData.areas;
                gameState.explorationProgress = explorationData.current_progress;
                gameState.userEnergy = explorationData.user_energy;
            } else {
                console.error('Failed to load exploration data:', explorationData.message);
            }

            renderAll();

            // Start timers if there's ongoing progress
            if (gameState.alchemyProgress.length > 0) {
                startAlchemyTimer();
            }
            if (gameState.explorationProgress.length > 0) {
                startExplorationTimer();
            }
        });
    }



    // --- INITIALIZATION ---
    // Load initial data when page loads (this will set the correct merit value)
    loadInitialData();

    // Collect passive income on page load (for offline earnings)
    setTimeout(collectPassiveIncome, 1000);

    // Start the game loop for passive income (runs 10 times per second)
    setInterval(gameLoop, 100);

    // Collect passive income from server every 30 seconds
    setInterval(collectPassiveIncome, 30000);

    // Update leaderboard more frequently - every 30 seconds
    setInterval(() => updateLeaderboard(false), 30000);

    // Update leaderboard on page load after a short delay
    setTimeout(() => updateLeaderboard(true), 2000);

    // Additional frequent updates for better real-time experience
    // Update every 10 seconds for the first 2 minutes after page load
    let quickUpdateCount = 0;
    const quickUpdateInterval = setInterval(() => {
        updateLeaderboard(false);
        quickUpdateCount++;
        if (quickUpdateCount >= 12) { // 12 * 10 seconds = 2 minutes
            clearInterval(quickUpdateInterval);
        }
    }, 10000);

    // Show golden mokugyo randomly (every 30-60 seconds)
    setInterval(() => {
        if (Math.random() < 0.02) { // 2% chance every 30 seconds = roughly every 25 minutes
            showGoldenMokugyo();
        }
    }, 30000);

});