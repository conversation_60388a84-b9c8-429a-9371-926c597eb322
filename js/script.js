// public/js/script.js

document.addEventListener('DOMContentLoaded', () => {
    // --- STATE ---
    let gameState = {
        merit: 0,
        meritPerSecond: 0,
        clickPower: 1,
        level: 1,
        experience: 0,
        levelUpThreshold: 100,
        prestigePoints: 0,
        buildings: [],
        upgrades: [],
        missions: [],
        achievements: [],
        stats: {}
    };

    // --- ELEMENTS ---
    const mokugyo = document.getElementById('mokugyo');
    const plusOne = document.getElementById('plus-one');
    const goldenMokugyo = document.getElementById('golden-mokugyo');
    const meritScoreDisplay = document.getElementById('merit-score-display');
    const meritPerSecondDisplay = document.getElementById('merit-per-second-display');
    const clickSound = document.getElementById('click-sound');
    const levelUpSound = document.getElementById('level-up-sound');

    // Debug element selection
    console.log('🔍 Element selection debug:');
    console.log('mokugyo element:', mokugyo);
    console.log('meritScoreDisplay element:', meritScoreDisplay);
    console.log('plusOne element:', plusOne);

    // --- TABS ---
    const allTabs = document.querySelectorAll('.tab-link');
    const allTabContents = document.querySelectorAll('.tab-content');

    allTabs.forEach(tab => {
        tab.addEventListener('click', () => {
            const targetTabId = tab.dataset.tab;
            const currentTabContainer = tab.closest('.tabs-panel, .right-panel');

            currentTabContainer.querySelectorAll('.tab-link').forEach(t => t.classList.remove('active'));
            tab.classList.add('active');

            currentTabContainer.querySelectorAll('.tab-content').forEach(c => c.classList.remove('active'));
            document.getElementById(targetTabId).classList.add('active');
        });
    });

    // --- NUMBER FORMATTING ---
    function formatNumber(num) {
        if (num < 1e6) return num.toLocaleString('en-US');
        if (num >= 1e100) return num.toExponential(2);
        const units = ["M", "B", "T", "Qa", "Qi", "Sx", "Sp", "Oc", "No", "Dc"];
        const order = Math.floor(Math.log10(Math.abs(num)) / 3);
        const unitname = units[Math.floor(order / 10)];
        const value = (num / Math.pow(10, order * 3)).toFixed(2);
        return `${value} ${unitname || ''}`;
    }

    // --- GAME LOGIC ---
    function mokugyoClick(e) {
        console.log('🥁 Mokugyo clicked! Event:', e); // Debug log

        // Visual feedback - change mokugyo briefly
        if (mokugyo) {
            mokugyo.style.transform = 'scale(0.95)';
            setTimeout(() => {
                mokugyo.style.transform = 'scale(1)';
            }, 100);
        }

        if (clickSound) {
            clickSound.currentTime = 0;
            clickSound.play();
        }

        const clickPower = calculateClickPower();
        console.log('💪 Click power calculated:', clickPower); // Debug log

        // Don't update local merit - wait for server response
        // gameState.merit += clickPower;
        // updateScore();

        // Animation
        const plusOneClone = plusOne.cloneNode(true);
        plusOneClone.textContent = `功德+${formatNumber(clickPower)}`;

        // Handle both mouse and touch events for positioning
        let clientX, clientY;
        if (e.touches && e.touches.length > 0) {
            // Touch event
            clientX = e.touches[0].clientX;
            clientY = e.touches[0].clientY;
        } else if (e.changedTouches && e.changedTouches.length > 0) {
            // Touch end event
            clientX = e.changedTouches[0].clientX;
            clientY = e.changedTouches[0].clientY;
        } else {
            // Mouse event
            clientX = e.clientX;
            clientY = e.clientY;
        }

        const rect = mokugyo.getBoundingClientRect();
        plusOneClone.style.left = `${clientX - rect.left}px`;
        plusOneClone.style.top = `${clientY - rect.top}px`;
        plusOneClone.classList.add('animate');
        mokugyo.parentElement.appendChild(plusOneClone);
        plusOneClone.addEventListener('animationend', () => plusOneClone.remove());

        // API Update
        console.log('📡 Sending API request to update_score.php...'); // Debug log

        // Visual indicator - add loading class
        if (mokugyo) {
            mokugyo.classList.add('loading');
        }

        fetch('api/update_score.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ increment: 1 }) // Send base click
        }).then(res => {
            console.log('📡 API response status:', res.status, 'OK:', res.ok); // Debug log
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            return res.json();
        }).then(data => {
            console.log('API response data:', data); // Debug log
            if (data.success) {
                // Update local state with server response
                if (data.money !== undefined) {
                    console.log('Previous merit:', gameState.merit, 'New merit from server:', data.money);
                    gameState.merit = data.money;
                    updateScore();
                    console.log('Merit display updated to:', gameState.merit); // Debug log
                }

                // Update player info if level up occurred or experience changed
                if (data.experience !== undefined || data.level_up || data.new_level) {
                    updatePlayerInfo({
                        level: data.new_level || gameState.level,
                        experience: data.experience || gameState.experience,
                        level_up_threshold: data.level_up_threshold || gameState.levelUpThreshold
                    });

                    // Show level up notification
                    if (data.level_up) {
                        showLevelUpNotification(data.new_level);
                    }
                }
            } else {
                console.error('Update score failed:', data.message);
                alert('點擊失敗: ' + data.message); // Show error to user
            }
        }).catch(error => {
            console.error('API call failed:', error);
            alert('網絡錯誤: ' + error.message); // Show error to user
        }).finally(() => {
            // Remove loading indicator
            if (mokugyo) {
                mokugyo.classList.remove('loading');
            }
        });
    }

    function calculateClickPower() {
        // Base click power
        let basePower = gameState.clickPower || 1;

        // Calculate multiplier from upgrades
        let multiplier = 1;
        if (gameState.upgrades) {
            gameState.upgrades.forEach(upgrade => {
                if (upgrade.owned && upgrade.effect_type === 'click_multiplier') {
                    multiplier *= upgrade.effect_value;
                }
            });
        }

        // Add prestige bonus
        let prestigeBonus = 1 + (gameState.prestigePoints * 0.01);

        return Math.floor(basePower * multiplier * prestigeBonus);
    }

    function updateScore() {
        const newValue = formatNumber(Math.floor(gameState.merit));
        console.log('📊 Updating score display:', gameState.merit, '→', newValue);
        if (meritScoreDisplay) {
            meritScoreDisplay.textContent = newValue;
            console.log('✅ Score display updated to:', meritScoreDisplay.textContent);
        } else {
            console.error('❌ meritScoreDisplay element not found!');
        }
    }

    // --- PLAYER INFO UPDATES ---
    function updatePlayerInfo(userData) {
        // Update level display
        const levelDisplay = document.getElementById('level-display');
        if (levelDisplay && userData.level !== undefined) {
            levelDisplay.textContent = userData.level;
            gameState.level = userData.level;
        }

        // Update experience bar
        const xpBar = document.getElementById('xp-bar');
        const xpText = document.getElementById('xp-text');
        if (xpBar && xpText && userData.experience !== undefined && userData.level_up_threshold !== undefined) {
            const xpPercentage = (userData.experience / userData.level_up_threshold) * 100;
            xpBar.style.width = Math.min(xpPercentage, 100) + '%';
            xpText.textContent = `${userData.experience} / ${userData.level_up_threshold}`;
            gameState.experience = userData.experience;
            gameState.levelUpThreshold = userData.level_up_threshold;
        }

        // Update prestige points
        const prestigeDisplay = document.getElementById('prestige-points-display');
        if (prestigeDisplay && userData.prestige_points !== undefined) {
            prestigeDisplay.textContent = userData.prestige_points;
            gameState.prestigePoints = userData.prestige_points;
        }
    }

    // --- LEADERBOARD UPDATES ---
    function updateLeaderboard(showLoading = false) {
        console.log('🏆 Updating leaderboard...');

        // Add visual loading indicator
        const leaderboardTable = document.getElementById('leaderboard-table');
        if (showLoading && leaderboardTable) {
            const tbody = leaderboardTable.querySelector('tbody');
            if (tbody) {
                tbody.style.opacity = '0.6';
            }
        }

        // Add cache-busting parameter
        const timestamp = Date.now();
        fetch(`api/get_clan_scores.php?t=${timestamp}`)
            .then(res => res.json())
            .then(data => {
                console.log('🏆 Leaderboard data received:', data);
                if (data.success) {
                    if (leaderboardTable) {
                        const tbody = leaderboardTable.querySelector('tbody');
                        if (tbody) {
                            tbody.innerHTML = '';
                            data.scores.forEach((score, index) => {
                                const row = document.createElement('tr');
                                row.innerHTML = `
                                    <td>${index + 1}</td>
                                    <td>${score.name}</td>
                                    <td>${formatNumber(score.total_marks)}</td>
                                `;
                                tbody.appendChild(row);
                            });
                            tbody.style.opacity = '1'; // Remove loading effect
                            console.log('🏆 Leaderboard updated successfully');
                        }
                    }
                } else {
                    console.error('Leaderboard update failed:', data.message);
                }
            })
            .catch(error => {
                console.error('Failed to update leaderboard:', error);
                // Remove loading effect even on error
                if (leaderboardTable) {
                    const tbody = leaderboardTable.querySelector('tbody');
                    if (tbody) {
                        tbody.style.opacity = '1';
                    }
                }
            });
    }

    function updateMeritPerSecond() {
        let baseMps = 0;
        gameState.buildings.forEach(building => {
            baseMps += building.level * building.base_production;
        });
        
        // TODO: Apply multipliers from upgrades/prestige
        gameState.meritPerSecond = baseMps;
        meritPerSecondDisplay.textContent = `每秒功德：${formatNumber(gameState.meritPerSecond)}`;
    }

    

    function renderAll() {
        renderBuildings();
        renderUpgrades();
        renderMissions();
        renderAchievements();
        renderStats();
        updateMeritPerSecond();
        updateScore();
    }

    function renderBuildings() {
        const container = document.getElementById('buildings-tab');
        container.innerHTML = '';
        gameState.buildings.forEach(b => {
            const cost = Math.floor(b.base_cost * Math.pow(b.cost_increase_ratio, b.level));
            const card = document.createElement('div');
            card.className = 'item-card';
            card.innerHTML = `
                <div class="item-info">
                    <h3>${b.name}</h3>
                    <p>功德: ${formatNumber(b.base_production * b.level)}/s</p>
                    <p>費用: ${formatNumber(cost)}</p>
                </div>
                <div class="item-level">${b.level}</div>
                <button class="buy-button" data-id="${b.id}" ${gameState.merit < cost ? 'disabled' : ''}>購買</button>
            `;
            container.appendChild(card);
        });
    }
    
    // Add renderUpgrades, renderMissions, etc. following the same pattern
    function renderUpgrades() {
        const container = document.getElementById('upgrades-tab');
        container.innerHTML = '';
        gameState.upgrades.forEach(u => {
            const card = document.createElement('div');
            card.className = 'item-card';
            card.innerHTML = `
                <div class="item-info">
                    <h3>${u.name}</h3>
                    <p>${u.description}</p>
                    <p>費用: ${formatNumber(u.cost)}</p>
                </div>
                <button class="buy-button" data-id="${u.id}" ${gameState.merit < u.cost ? 'disabled' : ''}>購買</button>
            `;
            container.appendChild(card);
        });
    }
    function renderMissions() {
        const container = document.getElementById('missions-tab');
        container.innerHTML = '';
        gameState.missions.forEach(m => {
            const card = document.createElement('div');
            card.className = 'item-card';
            const progress = m.progress > m.target ? m.target : m.progress;
            const isComplete = progress >= m.target;
            card.innerHTML = `
                <div class="item-info">
                    <h3>${m.name}</h3>
                    <p>${m.description}</p>
                    <p>進度: ${formatNumber(progress)} / ${formatNumber(m.target)}</p>
                </div>
                <button class="claim-button" data-id="${m.id}" ${!isComplete ? 'disabled' : ''}>領取</button>
            `;
            container.appendChild(card);
        });
    }
    function renderAchievements() {
        const container = document.getElementById('achievements-tab');
        container.innerHTML = '';
        gameState.achievements.forEach(a => {
            const card = document.createElement('div');
            card.className = 'item-card';
            const isUnlocked = a.unlocked; // Assuming this property is set
            card.innerHTML = `
                <div class="item-info">
                    <h3>${a.name}</h3>
                    <p>${a.description}</p>
                </div>
                <div class="status">${isUnlocked ? '已解鎖' : '未解鎖'}</div>
            `;
            container.appendChild(card);
        });
    }
    function renderStats() {
        const container = document.getElementById('stats-tab');
        container.innerHTML = `
            <p>總點擊次數: ${formatNumber(gameState.stats.total_clicks || 0)}</p>
            <p>總功德獲取量: ${formatNumber(gameState.stats.total_merit_earned || 0)}</p>
            <p>擁有建築物: ${formatNumber(gameState.stats.buildings_owned || 0)}</p>
            <p>已解鎖成就: ${formatNumber(gameState.stats.achievements_unlocked || 0)}</p>
        `;
    }


    // --- GOLDEN MOKUGYO ---
    function showGoldenMokugyo() {
        const x = Math.random() * 80 + 10;
        const y = Math.random() * 80 + 10;
        goldenMokugyo.style.left = `${x}%`;
        goldenMokugyo.style.top = `${y}%`;
        goldenMokugyo.style.display = 'block';
        setTimeout(() => {
            goldenMokugyo.style.display = 'none';
        }, 8000); // Visible for 8 seconds
    }

    function clickGoldenMokugyo() {
        fetch('api/golden_mokugyo.php').then(res => res.json()).then(data => {
            if (data.success) {
                gameState.merit += data.reward;
                alert(data.message);
                updateScore();
            }
        });
        goldenMokugyo.style.display = 'none';
    }

    // --- GAME LOOP ---
    function gameLoop() {
        // Only re-render buildings to update button states
        // Don't add passive income locally - server handles this
        renderBuildings();
    }

    // --- PASSIVE INCOME COLLECTION ---
    function collectPassiveIncome() {
        fetch('api/collect_passive_income.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' }
        })
        .then(res => {
            if (!res.ok) {
                throw new Error(`HTTP ${res.status}: ${res.statusText}`);
            }
            return res.json();
        })
        .then(data => {
            if (data.success && data.income_earned > 0) {
                console.log('Passive income collected:', data.income_earned);
                gameState.merit = data.money;
                updateScore();

                // Update player info if provided
                if (data.experience !== undefined) {
                    updatePlayerInfo({
                        level: data.level || gameState.level,
                        experience: data.experience,
                        level_up_threshold: data.level_up_threshold || gameState.levelUpThreshold
                    });
                }

                // Show notification if significant income was earned
                if (data.income_earned > 10) {
                    showPassiveIncomeNotification(data.income_earned);
                }
            } else if (!data.success) {
                console.error('Passive income collection failed:', data.message);
            }
        })
        .catch(error => {
            console.error('Passive income collection failed:', error);
        });
    }

    function showPassiveIncomeNotification(amount) {
        // Create a notification element
        const notification = document.createElement('div');
        notification.className = 'passive-income-notification';
        notification.textContent = `離線收益: 功德+${formatNumber(amount)}`;
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            font-weight: bold;
            z-index: 1000;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            animation: slideInRight 0.5s ease-out;
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'slideOutRight 0.5s ease-in';
            setTimeout(() => notification.remove(), 500);
        }, 3000);
    }

    function showLevelUpNotification(newLevel) {
        // Play level up sound if available
        if (levelUpSound) {
            levelUpSound.currentTime = 0;
            levelUpSound.play();
        }

        // Create level up notification
        const notification = document.createElement('div');
        notification.className = 'level-up-notification';
        notification.textContent = `🎉 升級了! 等級 ${newLevel}`;
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #333;
            padding: 20px 30px;
            border-radius: 12px;
            font-weight: bold;
            font-size: 1.5em;
            z-index: 1001;
            box-shadow: 0 8px 20px rgba(0,0,0,0.4);
            animation: levelUpPulse 2s ease-out;
        `;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.animation = 'fadeOut 0.5s ease-in';
            setTimeout(() => notification.remove(), 500);
        }, 2500);
    }

    // --- EVENT LISTENERS ---
    // Add both click and touch events for better mobile support
    if (mokugyo) {
        console.log('🎯 Adding event listeners to mokugyo element');
        mokugyo.addEventListener('click', mokugyoClick);
        mokugyo.addEventListener('touchend', function(e) {
            e.preventDefault(); // Prevent double-firing on mobile
            mokugyoClick(e);
        });
        console.log('✅ Event listeners added successfully');
    } else {
        console.error('❌ Mokugyo element not found! Cannot add event listeners.');
    }

    goldenMokugyo.addEventListener('click', clickGoldenMokugyo);
    goldenMokugyo.addEventListener('touchend', function(e) {
        e.preventDefault();
        clickGoldenMokugyo();
    });

    document.getElementById('buildings-tab').addEventListener('click', e => {
        if (e.target.classList.contains('buy-button')) {
            const buildingId = e.target.dataset.id;
            buyBuilding(buildingId);
        }
    });

    document.getElementById('upgrades-tab').addEventListener('click', e => {
        if (e.target.classList.contains('buy-button')) {
            const upgradeId = e.target.dataset.id;
            buyUpgrade(upgradeId);
        }
    });

    document.getElementById('missions-tab').addEventListener('click', e => {
        if (e.target.classList.contains('claim-button')) {
            const missionId = e.target.dataset.id;
            claimMission(missionId);
        }
    });

    // --- BUY/CLAIM FUNCTIONS ---
    function buyBuilding(id) {
        // Prevent multiple clicks by disabling the button
        const button = document.querySelector(`button[onclick="buyBuilding(${id})"]`);
        if (button) {
            if (button.disabled) {
                console.log('Building purchase already in progress');
                return; // Prevent multiple clicks
            }
            button.disabled = true;
            button.textContent = '購買中...';
            button.style.opacity = '0.6';
        }

        console.log('🏗️ Buying building:', id);

        fetch('api/buy_building.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ building_id: id })
        })
        .then(res => {
            console.log('🏗️ Building purchase response status:', res.status);
            return res.json();
        })
        .then(data => {
            console.log('🏗️ Building purchase response:', data);
            if (data.success) {
                gameState.merit = data.new_money;
                updateScore(); // Update score display immediately

                // Reload building data more efficiently
                fetch('api/get_buildings.php')
                    .then(res => res.json())
                    .then(buildingsData => {
                        if (buildingsData.success) {
                            gameState.buildings = buildingsData.buildings;
                            renderBuildings();
                            updateMeritPerSecond();
                        }
                    });

                // Update leaderboard since clan total marks may have changed
                setTimeout(() => updateLeaderboard(true), 500);
            } else {
                console.error('Buy building failed:', data.message);
                alert(data.message);
            }
        })
        .catch(error => {
            console.error('Buy building API call failed:', error);
            alert('購買失敗，請稍後再試');
        })
        .finally(() => {
            // Re-enable the button
            if (button) {
                button.disabled = false;
                button.textContent = '購買';
                button.style.opacity = '1';
            }
        });
    }

    function buyUpgrade(id) {
        fetch('api/buy_skill.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ skill_id: id })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                gameState.merit = data.money;
                loadInitialData();
                renderUpgrades();
            } else {
                alert(data.message);
            }
        });
    }

    function claimMission(id) {
        fetch('api/claim_mission_reward.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ user_mission_id: id })
        })
        .then(res => res.json())
        .then(data => {
            if (data.success) {
                gameState.merit = data.money;
                gameState.experience = data.experience;

                // Update player info with new experience/level
                updatePlayerInfo({
                    level: data.level || gameState.level,
                    experience: data.experience,
                    level_up_threshold: data.level_up_threshold || gameState.levelUpThreshold
                });

                loadInitialData();
                renderMissions();

                // Update leaderboard since merit may have changed
                setTimeout(() => updateLeaderboard(true), 500);
            } else {
                alert(data.message);
            }
        });
    }

    // --- DATA LOADING & RENDERING ---
    function loadInitialData() {
        Promise.all([
            fetch('api/get_buildings.php').then(res => res.json()),
            fetch('api/get_skills.php').then(res => res.json()),
            fetch('api/get_missions.php').then(res => res.json()),
            fetch('api/get_achievements.php').then(res => res.json()),
            fetch('api/get_stats.php').then(res => res.json())
        ]).then(([buildingsData, skillsData, missionsData, achievementsData, statsData]) => {
            if (statsData.success) {
                // Get current money from user data, not total_merit_earned
                fetch('api/user.php').then(res => res.json()).then(userData => {
                    if (userData.success) {
                        gameState.merit = parseInt(userData.user.money) || 0;
                        updateScore(); // Update display with current merit

                        // Update player info display with level and experience
                        updatePlayerInfo({
                            level: userData.user.level,
                            experience: userData.user.experience,
                            level_up_threshold: userData.user.level_up_threshold,
                            prestige_points: userData.user.prestige_points
                        });
                    } else {
                        console.error('Failed to load user data:', userData.message);
                    }
                }).catch(error => {
                    console.error('Failed to fetch user data:', error);
                });
                gameState.stats = statsData.stats;
            } else {
                console.error('Failed to load stats:', statsData.message);
            }
            if (buildingsData.success) {
                gameState.buildings = buildingsData.all_buildings.map(b => {
                    b.level = buildingsData.user_buildings[b.id] || 0;
                    return b;
                });
            } else {
                console.error('Failed to load buildings:', buildingsData.message);
            }
            if (skillsData.success) {
                gameState.upgrades = skillsData.all_skills.map(s => {
                    s.owned = skillsData.user_skills.includes(s.id.toString());
                    return s;
                });
            } else {
                console.error('Failed to load skills:', skillsData.message);
            }
            if (missionsData.success) {
                gameState.missions = missionsData.missions;
            } else {
                console.error('Failed to load missions:', missionsData.message);
            }
            if (achievementsData.success) {
                gameState.achievements = achievementsData.all_achievements.map(a => {
                    a.unlocked = achievementsData.user_achievements[a.id];
                    return a;
                });
            } else {
                console.error('Failed to load achievements:', achievementsData.message);
            }

            renderAll();
        });
    }

    // --- DEBUG FUNCTIONS ---
    window.debugMeritSync = function() {
        console.log('=== MERIT DEBUG INFO ===');
        console.log('gameState.merit:', gameState.merit);
        console.log('merit-score-display text:', document.getElementById('merit-score-display').textContent);

        // Test API call
        fetch('api/user.php')
            .then(res => res.json())
            .then(data => {
                console.log('User API response:', data);
                if (data.success) {
                    console.log('Server money:', data.user.money);
                    console.log('Server merit:', data.user.merit);
                    console.log('Difference:', data.user.money - gameState.merit);
                }
            });

        // Test mokugyo click
        console.log('Testing mokugyo click...');
        fetch('api/update_score.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ increment: 1 })
        })
        .then(res => res.json())
        .then(data => {
            console.log('Mokugyo API response:', data);
        });
    };

    // Test mokugyo click function directly
    window.testMokugyoClick = function() {
        console.log('🧪 Testing mokugyo click function directly...');
        mokugyoClick({ type: 'test', clientX: 100, clientY: 100 });
    };

    // Force update score display
    window.forceUpdateScore = function(newValue) {
        if (newValue !== undefined) {
            gameState.merit = newValue;
        }
        console.log('🔄 Force updating score to:', gameState.merit);
        updateScore();
    };

    // Test simple mokugyo API
    window.testSimpleMokugyo = function() {
        console.log('🥁 Testing simple mokugyo API...');
        fetch('api/test_mokugyo_simple.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ increment: 1 })
        })
        .then(res => res.json())
        .then(data => {
            console.log('🥁 Simple Mokugyo Test Result:', data);
            if (data.success) {
                alert('✅ Simple mokugyo API test PASSED!\nMoney updated from ' + data.old_money + ' to ' + data.new_money);
                // Update the display
                gameState.merit = data.money;
                updateScore();
                setTimeout(() => location.reload(), 1000);
            } else {
                alert('❌ Simple mokugyo API test FAILED!\n' + data.message);
            }
        })
        .catch(error => {
            console.error('🥁 Simple Mokugyo Test Error:', error);
            alert('❌ Simple mokugyo test error: ' + error.message);
        });
    };

    // Test database transaction
    window.testDBTransaction = function() {
        console.log('🗄️ Testing database transaction...');
        fetch('test_db_transaction.php')
            .then(res => res.json())
            .then(data => {
                console.log('🗄️ DB Transaction Test Result:', data);
                if (data.success) {
                    alert('✅ Database transaction test PASSED!\nMoney updated from ' + data.user_before.money + ' to ' + data.user_after.money);
                    // Refresh the page to show updated values
                    setTimeout(() => location.reload(), 1000);
                } else {
                    alert('❌ Database transaction test FAILED!\n' + data.message);
                }
            })
            .catch(error => {
                console.error('🗄️ DB Transaction Test Error:', error);
                alert('❌ Database test error: ' + error.message);
            });
    };

    // --- INITIALIZATION ---
    // Load initial data when page loads (this will set the correct merit value)
    loadInitialData();

    // Collect passive income on page load (for offline earnings)
    setTimeout(collectPassiveIncome, 1000);

    // Start the game loop for passive income (runs 10 times per second)
    setInterval(gameLoop, 100);

    // Collect passive income from server every 30 seconds
    setInterval(collectPassiveIncome, 30000);

    // Update leaderboard more frequently - every 30 seconds
    setInterval(() => updateLeaderboard(false), 30000);

    // Update leaderboard on page load after a short delay
    setTimeout(() => updateLeaderboard(true), 2000);

    // Additional frequent updates for better real-time experience
    // Update every 10 seconds for the first 2 minutes after page load
    let quickUpdateCount = 0;
    const quickUpdateInterval = setInterval(() => {
        updateLeaderboard(false);
        quickUpdateCount++;
        if (quickUpdateCount >= 12) { // 12 * 10 seconds = 2 minutes
            clearInterval(quickUpdateInterval);
        }
    }, 10000);

    // Show golden mokugyo randomly (every 30-60 seconds)
    setInterval(() => {
        if (Math.random() < 0.02) { // 2% chance every 30 seconds = roughly every 25 minutes
            showGoldenMokugyo();
        }
    }, 30000);

});