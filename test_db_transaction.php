<?php
// test_db_transaction.php - Test database transactions

session_start();
require_once './api/db_connect.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'User not logged in']);
    exit;
}

$user_id = $_SESSION['user_id'];

try {
    // Test 1: Check current user data
    $stmt = $pdo->prepare("SELECT id, username, money, merit, level, experience FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user_before = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$user_before) {
        echo json_encode(['success' => false, 'message' => 'User not found', 'user_id' => $user_id]);
        exit;
    }
    
    // Test 2: Try a simple transaction
    $pdo->beginTransaction();
    
    $new_money = $user_before['money'] + 1;
    $stmt_update = $pdo->prepare("UPDATE users SET money = ?, merit = ? WHERE id = ?");
    $update_result = $stmt_update->execute([$new_money, $new_money, $user_id]);
    $rows_affected = $stmt_update->rowCount();
    
    if ($update_result && $rows_affected > 0) {
        $pdo->commit();
        
        // Verify the update
        $stmt_verify = $pdo->prepare("SELECT money, merit FROM users WHERE id = ?");
        $stmt_verify->execute([$user_id]);
        $user_after = $stmt_verify->fetch(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'message' => 'Transaction test successful',
            'user_before' => $user_before,
            'user_after' => $user_after,
            'update_result' => $update_result,
            'rows_affected' => $rows_affected,
            'expected_money' => $new_money,
            'actual_money' => $user_after['money'],
            'money_matches' => ($user_after['money'] == $new_money)
        ]);
    } else {
        $pdo->rollBack();
        echo json_encode([
            'success' => false,
            'message' => 'Update failed',
            'user_before' => $user_before,
            'update_result' => $update_result,
            'rows_affected' => $rows_affected
        ]);
    }
    
} catch (Exception $e) {
    if ($pdo->inTransaction()) {
        $pdo->rollBack();
    }
    echo json_encode([
        'success' => false,
        'message' => 'Exception: ' . $e->getMessage(),
        'user_id' => $user_id
    ]);
}
?>
