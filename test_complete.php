<?php
// test_complete.php - Comprehensive test for all game functionality

session_start();
require_once './api/db_connect.php';

echo "<!DOCTYPE html>
<html lang='zh-Hant'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Complete Game Test</title>
    <style>
        body { font-family: Arial, sans-serif; background: #121212; color: #E0E0E0; padding: 20px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #FFC107; }
        .info { color: #03A9F4; }
        h2 { color: #FFC107; border-bottom: 1px solid #444; padding-bottom: 10px; }
        pre { background: #1E1E1E; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .test-section { margin: 20px 0; padding: 15px; background: #1E1E1E; border-radius: 8px; }
        .test-button { background: #FFC107; color: #121212; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .mokugyo-test { width: 100px; height: 100px; background: #FFC107; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; margin: 20px auto; font-size: 24px; }
    </style>
</head>
<body>";

echo "<h1>🎮 Complete Game Functionality Test</h1>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p class='error'>❌ Please <a href='login.php' style='color: #03A9F4;'>login</a> first to run tests</p>";
    echo "</body></html>";
    exit;
}

$user_id = $_SESSION['user_id'];

// Test 1: User Data
echo "<div class='test-section'>";
echo "<h2>👤 User Data Test</h2>";
try {
    $stmt = $pdo->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "<p class='success'>✅ User found</p>";
        echo "<pre>User ID: {$user['id']}
Username: {$user['username']}
Money: {$user['money']}
Merit: {$user['merit']}
Level: {$user['level']}
Total Clicks: {$user['total_clicks']}
Total Merit Earned: {$user['total_merit_earned']}</pre>";
    } else {
        echo "<p class='error'>❌ User not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 2: API Endpoints
echo "<div class='test-section'>";
echo "<h2>🔌 API Endpoints Test</h2>";
echo "<button class='test-button' onclick='testAPI(\"update_score\")'>Test update_score.php</button>";
echo "<button class='test-button' onclick='testAPI(\"test_click\")'>Test test_click.php</button>";
echo "<button class='test-button' onclick='testAPI(\"user\")'>Test user.php</button>";
echo "<button class='test-button' onclick='testAPI(\"missions\")'>Test get_missions.php</button>";
echo "<button class='test-button' onclick='testAPI(\"assign_missions\")'>Assign Missions</button>";
echo "<div id='api-results'></div>";
echo "</div>";

// Test 3: Interactive Mokugyo Test
echo "<div class='test-section'>";
echo "<h2>🥁 Interactive Mokugyo Test</h2>";
echo "<p class='info'>Click the mokugyo below and watch the console for debug information:</p>";
echo "<div class='mokugyo-test' id='test-mokugyo'>🥁</div>";
echo "<div id='click-results'></div>";
echo "</div>";

// Test 4: Database Direct Update
echo "<div class='test-section'>";
echo "<h2>💾 Database Direct Update Test</h2>";
echo "<button class='test-button' onclick='testDirectUpdate()'>Add 100 Merit Directly</button>";
echo "<div id='direct-results'></div>";
echo "</div>";

// JavaScript for testing
echo "<script>
let clickCount = 0;

function testAPI(endpoint) {
    const resultsDiv = document.getElementById('api-results');
    resultsDiv.innerHTML = '<p class=\"info\">Testing ' + endpoint + '...</p>';
    
    let url, data;
    switch(endpoint) {
        case 'update_score':
            url = 'api/update_score.php';
            data = { increment: 1 };
            break;
        case 'test_click':
            url = 'api/test_click.php';
            data = { increment: 5 };
            break;
        case 'user':
            url = 'api/user.php';
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = '<p class=\"success\">✅ ' + endpoint + ' response:</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<p class=\"error\">❌ ' + endpoint + ' error: ' + error.message + '</p>';
                });
            return;
        case 'missions':
            url = 'api/get_missions.php';
            fetch(url)
                .then(response => response.json())
                .then(data => {
                    resultsDiv.innerHTML = '<p class=\"success\">✅ ' + endpoint + ' response:</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                })
                .catch(error => {
                    resultsDiv.innerHTML = '<p class=\"error\">❌ ' + endpoint + ' error: ' + error.message + '</p>';
                });
            return;
        case 'assign_missions':
            url = 'api/assign_missions.php';
            data = {};
            break;
    }
    
    fetch(url, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data)
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            resultsDiv.innerHTML = '<p class=\"success\">✅ ' + endpoint + ' successful:</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
        } else {
            resultsDiv.innerHTML = '<p class=\"error\">❌ ' + endpoint + ' failed: ' + (data.message || 'Unknown error') + '</p>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultsDiv.innerHTML = '<p class=\"error\">❌ ' + endpoint + ' network error: ' + error.message + '</p>';
    });
}

function testDirectUpdate() {
    const resultsDiv = document.getElementById('direct-results');
    resultsDiv.innerHTML = '<p class=\"info\">Updating database directly...</p>';
    
    fetch('test_complete.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'direct_update=1'
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('SUCCESS')) {
            resultsDiv.innerHTML = '<p class=\"success\">✅ Direct update successful</p>';
            setTimeout(() => location.reload(), 1000);
        } else {
            resultsDiv.innerHTML = '<p class=\"error\">❌ Direct update failed</p>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = '<p class=\"error\">❌ Direct update error: ' + error.message + '</p>';
    });
}

// Mokugyo click test
document.getElementById('test-mokugyo').addEventListener('click', function() {
    clickCount++;
    const resultsDiv = document.getElementById('click-results');
    resultsDiv.innerHTML = '<p class=\"info\">Click #' + clickCount + ' - Check console for details</p>';
    
    console.log('=== Mokugyo Click Test #' + clickCount + ' ===');
    console.log('Sending request to update_score.php...');
    
    fetch('api/update_score.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ increment: 1 })
    })
    .then(response => {
        console.log('Response status:', response.status);
        console.log('Response headers:', response.headers);
        return response.json();
    })
    .then(data => {
        console.log('Response data:', data);
        if (data.success) {
            resultsDiv.innerHTML = '<p class=\"success\">✅ Click #' + clickCount + ' successful! New money: ' + data.money + '</p>';
        } else {
            resultsDiv.innerHTML = '<p class=\"error\">❌ Click #' + clickCount + ' failed: ' + (data.message || 'Unknown error') + '</p>';
        }
    })
    .catch(error => {
        console.error('Error:', error);
        resultsDiv.innerHTML = '<p class=\"error\">❌ Click #' + clickCount + ' network error: ' + error.message + '</p>';
    });
});
</script>";

// Handle direct update request
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['direct_update'])) {
    try {
        $stmt = $pdo->prepare("UPDATE users SET money = money + 100, merit = merit + 100 WHERE id = ?");
        $result = $stmt->execute([$user_id]);
        if ($result) {
            echo "SUCCESS";
        } else {
            echo "FAILED";
        }
    } catch (Exception $e) {
        echo "ERROR: " . $e->getMessage();
    }
    exit;
}

echo "<p class='info'><a href='index.php' style='color: #03A9F4;'>← Back to Game</a> | <a href='debug_mokugyo.php' style='color: #03A9F4;'>Debug Script</a></p>";
echo "</body></html>";
?>
