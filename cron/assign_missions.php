<?php
// public/cron/assign_missions.php

require_once __DIR__ . '/../api/db_connect.php';

function assign_daily_missions($pdo) {
    // Get all users
    $stmt_users = $pdo->query("SELECT id FROM users;");
    $users = $stmt_users->fetchAll(PDO::FETCH_ASSOC);

    // Get all missions
    $stmt_missions = $pdo->query("SELECT id FROM missions;");
    $missions = $stmt_missions->fetchAll(PDO::FETCH_COLUMN, 0);

    if (count($missions) > 0) {
        foreach ($users as $user) {
            $user_id = $user['id'];

            // For each mission, check if the user already has it for today
            foreach ($missions as $mission_id) {
                $stmt_check_mission = $pdo->prepare("
                    SELECT id FROM user_missions 
                    WHERE user_id = :user_id AND mission_id = :mission_id AND assigned_at = CURRENT_DATE;
                ");
                $stmt_check_mission->execute([':user_id' => $user_id, ':mission_id' => $mission_id]);

                if ($stmt_check_mission->rowCount() == 0) {
                    // Assign the mission if they don't have it
                    $stmt_assign_mission = $pdo->prepare("
                        INSERT INTO user_missions (user_id, mission_id, assigned_at) 
                        VALUES (:user_id, :mission_id, CURRENT_DATE);
                    ");
                    $stmt_assign_mission->execute([':user_id' => $user_id, ':mission_id' => $mission_id]);
                }
            }
        }
    }
}

// Ensure $pdo is available from db_connect.php
if (isset($pdo) && $pdo) {
    assign_daily_missions($pdo);
} else {
    error_log("assign_missions.php: PDO connection not available.");
}
?>