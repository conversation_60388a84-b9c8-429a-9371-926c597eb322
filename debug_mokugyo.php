<?php
// debug_mokugyo.php - Debug script for mokugyo click functionality

session_start();
require_once './api/db_connect.php';

echo "<h1>Mokugyo Click Debug Test</h1>";
echo "<style>
body { font-family: Arial, sans-serif; background: #121212; color: #E0E0E0; padding: 20px; }
.success { color: #4CAF50; }
.error { color: #f44336; }
.warning { color: #FFC107; }
.info { color: #03A9F4; }
h2 { color: #FFC107; border-bottom: 1px solid #444; padding-bottom: 10px; }
pre { background: #1E1E1E; padding: 15px; border-radius: 5px; overflow-x: auto; }
.test-button { background: #FFC107; color: #121212; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
</style>";

// Test 1: Database Connection
echo "<h2>1. Database Connection Test</h2>";
if ($pdo) {
    echo "<p class='success'>✓ Database connection successful</p>";
} else {
    echo "<p class='error'>✗ Database connection failed</p>";
    exit;
}

// Test 2: Session Check
echo "<h2>2. Session Test</h2>";
if (isset($_SESSION['user_id'])) {
    echo "<p class='success'>✓ User logged in with ID: " . $_SESSION['user_id'] . "</p>";
    $user_id = $_SESSION['user_id'];
} else {
    echo "<p class='error'>✗ No user logged in</p>";
    echo "<p class='info'>Please <a href='login.php' style='color: #03A9F4;'>login</a> first to test mokugyo functionality</p>";
    exit;
}

// Test 3: User Data Check
echo "<h2>3. User Data Check</h2>";
try {
    $stmt = $pdo->prepare("SELECT id, username, money, merit, level, experience, total_clicks, total_merit_earned FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "<p class='success'>✓ User found in database</p>";
        echo "<pre>";
        echo "Username: " . $user['username'] . "\n";
        echo "Money: " . $user['money'] . "\n";
        echo "Merit: " . $user['merit'] . "\n";
        echo "Level: " . $user['level'] . "\n";
        echo "Experience: " . $user['experience'] . "\n";
        echo "Total Clicks: " . $user['total_clicks'] . "\n";
        echo "Total Merit Earned: " . $user['total_merit_earned'] . "\n";
        echo "</pre>";
    } else {
        echo "<p class='error'>✗ User not found in database</p>";
        exit;
    }
} catch (PDOException $e) {
    echo "<p class='error'>✗ Database error: " . $e->getMessage() . "</p>";
    exit;
}

// Test 4: API Endpoint Test
echo "<h2>4. API Endpoint Test</h2>";

// Test update_score.php directly
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_click'])) {
    echo "<h3>Testing update_score.php API call...</h3>";
    
    // Simulate API call
    $test_data = json_encode(['increment' => 1]);
    
    // Create a context for the API call
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Cookie: ' . $_SERVER['HTTP_COOKIE']
            ],
            'content' => $test_data
        ]
    ]);
    
    $api_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/api/update_score.php';
    $result = file_get_contents($api_url, false, $context);
    
    if ($result) {
        $response = json_decode($result, true);
        echo "<p class='info'>API Response:</p>";
        echo "<pre>" . json_encode($response, JSON_PRETTY_PRINT) . "</pre>";
        
        if (isset($response['success']) && $response['success']) {
            echo "<p class='success'>✓ API call successful</p>";
        } else {
            echo "<p class='error'>✗ API call failed: " . ($response['message'] ?? 'Unknown error') . "</p>";
        }
    } else {
        echo "<p class='error'>✗ No response from API</p>";
    }
    
    // Check user data after API call
    echo "<h3>User data after API call:</h3>";
    $stmt = $pdo->prepare("SELECT money, merit, total_clicks, total_merit_earned FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $updated_user = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "<pre>" . json_encode($updated_user, JSON_PRETTY_PRINT) . "</pre>";
}

// Test 5: Direct Database Update Test
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['test_direct'])) {
    echo "<h3>Testing direct database update...</h3>";
    
    try {
        $pdo->beginTransaction();
        
        $stmt = $pdo->prepare("UPDATE users SET money = money + 10, merit = merit + 10, total_clicks = total_clicks + 1, total_merit_earned = total_merit_earned + 10 WHERE id = ?");
        $result = $stmt->execute([$user_id]);
        
        if ($result) {
            echo "<p class='success'>✓ Direct database update successful</p>";
            $pdo->commit();
            
            // Show updated data
            $stmt = $pdo->prepare("SELECT money, merit, total_clicks, total_merit_earned FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $updated_user = $stmt->fetch(PDO::FETCH_ASSOC);
            echo "<pre>" . json_encode($updated_user, JSON_PRETTY_PRINT) . "</pre>";
        } else {
            echo "<p class='error'>✗ Direct database update failed</p>";
            $pdo->rollBack();
        }
    } catch (PDOException $e) {
        echo "<p class='error'>✗ Database error: " . $e->getMessage() . "</p>";
        $pdo->rollBack();
    }
}

// Test 6: JavaScript Console Test
echo "<h2>5. JavaScript Console Test</h2>";
echo "<p class='info'>Open browser console (F12) and check for JavaScript errors when clicking the test button below:</p>";
echo "<button id='test-mokugyo' class='test-button'>Test Mokugyo Click (Check Console)</button>";

echo "<button id='test-simple' class='test-button'>Test Simple API (Check Console)</button>";

echo "<script>
document.getElementById('test-mokugyo').addEventListener('click', function() {
    console.log('Testing mokugyo click...');

    fetch('api/update_score.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ increment: 1 })
    })
    .then(response => {
        console.log('Response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('API Response:', data);
        if (data.success) {
            alert('Success! Check the updated user data above.');
            location.reload();
        } else {
            alert('Failed: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Network error: ' + error.message);
    });
});

document.getElementById('test-simple').addEventListener('click', function() {
    console.log('Testing simple API...');

    fetch('api/test_click.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ increment: 5 })
    })
    .then(response => {
        console.log('Simple API response status:', response.status);
        return response.json();
    })
    .then(data => {
        console.log('Simple API Response:', data);
        if (data.success) {
            alert('Simple API Success! Merit increased by 5.');
            location.reload();
        } else {
            alert('Simple API Failed: ' + (data.message || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Simple API Error:', error);
        alert('Simple API Network error: ' + error.message);
    });
});
</script>";

// Test buttons
echo "<h2>6. Manual Tests</h2>";
echo "<form method='post' style='margin: 10px 0;'>";
echo "<button type='submit' name='test_click' class='test-button'>Test API Call</button>";
echo "<button type='submit' name='test_direct' class='test-button'>Test Direct DB Update</button>";
echo "</form>";

echo "<p class='info'><a href='index.php' style='color: #03A9F4;'>← Back to Game</a></p>";
?>
