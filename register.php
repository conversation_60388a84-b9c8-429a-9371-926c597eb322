<?php
// public/register.php

session_start();

require_once './api/db_connect.php';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'];
    $password = password_hash($_POST['password'], PASSWORD_DEFAULT);

    $stmt_check_user = $pdo->prepare("SELECT id FROM users WHERE username = :username");
    $stmt_check_user->execute([':username' => $username]);

    if ($stmt_check_user->rowCount() > 0) {
        $error = "用戶名已存在";
    } else {
        $stmt_insert_user = $pdo->prepare("INSERT INTO users (username, password_hash) VALUES (:username, :password_hash) RETURNING id;");
        $stmt_insert_user->execute([':username' => $username, ':password_hash' => $password]);
        $user = $stmt_insert_user->fetch(PDO::FETCH_ASSOC);
        
        $_SESSION['user_id'] = $user['id'];
        header('Location: templates/clan_selection.php');
        exit;
    }
}
?>

<!DOCTYPE html>
<html lang="zh-Hant">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>註冊</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <h1>註冊</h1>
        <?php if (isset($error)): ?>
            <p class="error"><?php echo $error; ?></p>
        <?php endif; ?>
        <form action="register.php" method="post">
            <div class="form-group">
                <label for="username">用戶名</label>
                <input type="text" name="username" id="username" required>
            </div>
            <div class="form-group">
                <label for="password">密碼</label>
                <input type="password" name="password" id="password" required>
            </div>
            <button type="submit">註冊</button>
        </form>
        <p>已經有帳號？ <a href="login.php">立即登入</a></p>
    </div>
</body>
</html>