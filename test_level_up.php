<?php
// test_level_up.php - Test level-up functionality

session_start();
require_once './api/db_connect.php';

echo "<!DOCTYPE html>
<html lang='zh-Hant'>
<head>
    <meta charset='UTF-8'>
    <meta name='viewport' content='width=device-width, initial-scale=1.0'>
    <title>Level-Up Test</title>
    <style>
        body { font-family: Arial, sans-serif; background: #121212; color: #E0E0E0; padding: 20px; }
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #FFC107; }
        .info { color: #03A9F4; }
        h2 { color: #FFC107; border-bottom: 1px solid #444; padding-bottom: 10px; }
        pre { background: #1E1E1E; padding: 15px; border-radius: 5px; overflow-x: auto; }
        .test-button { background: #FFC107; color: #121212; padding: 10px 20px; border: none; border-radius: 5px; cursor: pointer; margin: 5px; }
        .test-section { margin: 20px 0; padding: 15px; background: #1E1E1E; border-radius: 8px; }
    </style>
</head>
<body>";

echo "<h1>🆙 Level-Up System Test</h1>";

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    echo "<p class='error'>❌ Please <a href='login.php' style='color: #03A9F4;'>login</a> first</p>";
    echo "</body></html>";
    exit;
}

$user_id = $_SESSION['user_id'];

// Test 1: Current User Level Data
echo "<div class='test-section'>";
echo "<h2>📊 Current Level Data</h2>";
try {
    $stmt = $pdo->prepare("SELECT id, username, level, experience, level_up_threshold, money FROM users WHERE id = ?");
    $stmt->execute([$user_id]);
    $user = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($user) {
        echo "<p class='success'>✅ User data retrieved</p>";
        echo "<pre>";
        foreach ($user as $key => $value) {
            echo "$key: $value\n";
        }
        echo "</pre>";
        
        $exp_percentage = ($user['experience'] / $user['level_up_threshold']) * 100;
        echo "<p class='info'>📈 Experience Progress: {$user['experience']}/{$user['level_up_threshold']} ({$exp_percentage:.1f}%)</p>";
        
        if ($user['experience'] >= $user['level_up_threshold']) {
            echo "<p class='warning'>⚠️ Ready to level up!</p>";
        } else {
            $needed = $user['level_up_threshold'] - $user['experience'];
            echo "<p class='info'>🎯 Need $needed more experience to level up</p>";
        }
    } else {
        echo "<p class='error'>❌ User not found</p>";
    }
} catch (Exception $e) {
    echo "<p class='error'>❌ Database error: " . $e->getMessage() . "</p>";
}
echo "</div>";

// Test 2: Level-Up Simulation
echo "<div class='test-section'>";
echo "<h2>🧪 Level-Up Test</h2>";
echo "<button class='test-button' onclick='testLevelUp()'>Test Level-Up with Mokugyo Clicks</button>";
echo "<button class='test-button' onclick='forceExperience()'>Force Experience to Threshold</button>";
echo "<div id='levelup-results'></div>";
echo "</div>";

// Test 3: Manual Experience Addition
echo "<div class='test-section'>";
echo "<h2>✏️ Manual Experience Test</h2>";
echo "<button class='test-button' onclick='addExperience(50)'>Add 50 Experience</button>";
echo "<button class='test-button' onclick='addExperience(100)'>Add 100 Experience</button>";
echo "<div id='manual-results'></div>";
echo "</div>";

echo "<script>
function testLevelUp() {
    const resultsDiv = document.getElementById('levelup-results');
    resultsDiv.innerHTML = '<p class=\"info\">Testing level-up with mokugyo clicks...</p>';
    
    // Make multiple mokugyo clicks to trigger level-up
    let clickCount = 0;
    const maxClicks = 10;
    
    function makeClick() {
        fetch('api/update_score.php', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ increment: 10 }) // Higher increment for faster testing
        })
        .then(response => response.json())
        .then(data => {
            console.log('Click ' + (clickCount + 1) + ' response:', data);
            clickCount++;
            
            if (data.level_up) {
                resultsDiv.innerHTML = '<p class=\"success\">🎉 LEVEL UP DETECTED!</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
                return;
            }
            
            if (clickCount < maxClicks) {
                setTimeout(makeClick, 500); // Wait 500ms between clicks
            } else {
                resultsDiv.innerHTML = '<p class=\"warning\">⚠️ No level-up after ' + maxClicks + ' clicks</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            }
        })
        .catch(error => {
            resultsDiv.innerHTML = '<p class=\"error\">❌ Error: ' + error.message + '</p>';
        });
    }
    
    makeClick();
}

function forceExperience() {
    const resultsDiv = document.getElementById('levelup-results');
    resultsDiv.innerHTML = '<p class=\"info\">Forcing experience to threshold...</p>';
    
    fetch('test_level_up.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'force_experience=1'
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('SUCCESS')) {
            resultsDiv.innerHTML = '<p class=\"success\">✅ Experience set to threshold</p>';
            setTimeout(() => location.reload(), 1000);
        } else {
            resultsDiv.innerHTML = '<p class=\"error\">❌ Failed to set experience: ' + data + '</p>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = '<p class=\"error\">❌ Error: ' + error.message + '</p>';
    });
}

function addExperience(amount) {
    const resultsDiv = document.getElementById('manual-results');
    resultsDiv.innerHTML = '<p class=\"info\">Adding ' + amount + ' experience...</p>';
    
    fetch('test_level_up.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
        body: 'add_experience=' + amount
    })
    .then(response => response.text())
    .then(data => {
        if (data.includes('SUCCESS')) {
            resultsDiv.innerHTML = '<p class=\"success\">✅ Experience added successfully</p>';
            setTimeout(() => location.reload(), 1000);
        } else {
            resultsDiv.innerHTML = '<p class=\"error\">❌ Failed to add experience: ' + data + '</p>';
        }
    })
    .catch(error => {
        resultsDiv.innerHTML = '<p class=\"error\">❌ Error: ' + error.message + '</p>';
    });
}
</script>";

// Handle POST requests
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['force_experience'])) {
        try {
            // Get current threshold and set experience to threshold - 1
            $stmt = $pdo->prepare("SELECT level_up_threshold FROM users WHERE id = ?");
            $stmt->execute([$user_id]);
            $threshold = $stmt->fetchColumn();
            
            $stmt = $pdo->prepare("UPDATE users SET experience = ? WHERE id = ?");
            $result = $stmt->execute([$threshold - 1, $user_id]);
            if ($result) {
                echo "SUCCESS";
            } else {
                echo "FAILED";
            }
        } catch (Exception $e) {
            echo "ERROR: " . $e->getMessage();
        }
        exit;
    }
    
    if (isset($_POST['add_experience'])) {
        $amount = (int)$_POST['add_experience'];
        try {
            $stmt = $pdo->prepare("UPDATE users SET experience = experience + ? WHERE id = ?");
            $result = $stmt->execute([$amount, $user_id]);
            if ($result) {
                echo "SUCCESS";
            } else {
                echo "FAILED";
            }
        } catch (Exception $e) {
            echo "ERROR: " . $e->getMessage();
        }
        exit;
    }
}

echo "<p class='info'><a href='index.php' style='color: #03A9F4;'>← Back to Game</a></p>";
echo "</body></html>";
?>
