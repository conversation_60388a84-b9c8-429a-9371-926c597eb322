<?php
// debug_test.php - Simple test script to verify API functionality

session_start();
require_once './api/db_connect.php';

echo "<h1>Mokugyo Game Debug Test</h1>";

// Test database connection
if ($pdo) {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test if tables exist
    try {
        $stmt = $pdo->query("SELECT COUNT(*) FROM users");
        $userCount = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ Users table exists with $userCount users</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM buildings");
        $buildingCount = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ Buildings table exists with $buildingCount buildings</p>";
        
        $stmt = $pdo->query("SELECT COUNT(*) FROM clans");
        $clanCount = $stmt->fetchColumn();
        echo "<p style='color: green;'>✓ Clans table exists with $clanCount clans</p>";
        
    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ Database table error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: red;'>✗ Database connection failed</p>";
}

// Test session
if (isset($_SESSION['user_id'])) {
    echo "<p style='color: green;'>✓ User logged in with ID: " . $_SESSION['user_id'] . "</p>";

    // Test user data retrieval
    try {
        $stmt = $pdo->prepare("SELECT username, money, merit, level FROM users WHERE id = ?");
        $stmt->execute([$_SESSION['user_id']]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user) {
            echo "<p style='color: green;'>✓ User data: " . $user['username'] . " (Level: " . $user['level'] . ", Money: " . $user['money'] . ", Merit: " . $user['merit'] . ")</p>";
        } else {
            echo "<p style='color: red;'>✗ User not found in database</p>";
        }

        // Test daily missions
        $stmt_missions = $pdo->prepare("
            SELECT COUNT(*) as mission_count
            FROM user_missions um
            WHERE um.user_id = ? AND um.assigned_at = CURRENT_DATE
        ");
        $stmt_missions->execute([$_SESSION['user_id']]);
        $mission_count = $stmt_missions->fetchColumn();

        if ($mission_count > 0) {
            echo "<p style='color: green;'>✓ Daily missions assigned: $mission_count missions</p>";
        } else {
            echo "<p style='color: orange;'>⚠ No daily missions assigned (will be assigned on next login)</p>";
        }

    } catch (PDOException $e) {
        echo "<p style='color: red;'>✗ User query error: " . $e->getMessage() . "</p>";
    }
} else {
    echo "<p style='color: orange;'>⚠ No user logged in</p>";
}

// Test API endpoints
echo "<h2>API Endpoint Tests</h2>";

$apiTests = [
    'get_buildings.php' => 'Buildings API',
    'get_skills.php' => 'Skills API', 
    'get_stats.php' => 'Stats API',
    'user.php' => 'User API'
];

foreach ($apiTests as $endpoint => $name) {
    $url = "http://localhost/api/$endpoint";
    echo "<p>Testing $name ($endpoint)...</p>";
    
    // For session-based APIs, we can't easily test via HTTP without proper session handling
    // Instead, let's test the file existence and basic syntax
    if (file_exists("./api/$endpoint")) {
        echo "<p style='color: green;'>✓ $name file exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $name file missing</p>";
    }
}

echo "<h2>File Structure Check</h2>";

$requiredFiles = [
    'js/script.js' => 'JavaScript file',
    'css/style.css' => 'CSS file',
    'img/mokugyo.png' => 'Mokugyo image',
    'sound/click.mp3' => 'Click sound'
];

foreach ($requiredFiles as $file => $description) {
    if (file_exists($file)) {
        echo "<p style='color: green;'>✓ $description exists</p>";
    } else {
        echo "<p style='color: red;'>✗ $description missing</p>";
    }
}

?>
